<?php
/**
 * Database Configuration File
 * 
 * This file contains the database connection settings for the Employee Management System
 */

// Database connection settings
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'payroll_system');

// Establish database connection
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Set character set to utf8mb4 for Arabic support
    $conn->set_charset("utf8mb4");
} catch (Exception $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    die("Database connection failed. Please check your configuration or contact the administrator.");
}

// Set default timezone
date_default_timezone_set('Asia/Riyadh');

// System configuration
define('SYSTEM_NAME', 'نظام إدارة الموظفين والرواتب');
define('SYSTEM_VERSION', '1.0.0');
define('CURRENCY_SYMBOL', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('RECORDS_PER_PAGE', 10);

// File upload paths
define('UPLOAD_DIR', dirname(__DIR__) . '/assets/uploads/');
define('EMPLOYEE_IMAGES', UPLOAD_DIR . 'employees/');
define('BACKUP_DIR', UPLOAD_DIR . 'backups/');

// Create upload directories if they don't exist
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
if (!file_exists(EMPLOYEE_IMAGES)) {
    mkdir(EMPLOYEE_IMAGES, 0755, true);
}
if (!file_exists(BACKUP_DIR)) {
    mkdir(BACKUP_DIR, 0755, true);
}
?>
