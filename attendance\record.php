<?php
/**
 * Record Attendance Page
 * 
 * This file handles recording attendance for employees
 */

// Include header
require_once '../includes/header.php';

// Initialize variables
$date = isset($_GET['date']) ? sanitize($_GET['date']) : date('Y-m-d');
$employee_id = isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0;
$errors = [];
$success = [];

// Get active employees
$sql = "SELECT id, employee_number, name, department, position FROM employees WHERE status = 'active' ORDER BY name";
$result = $conn->query($sql);
$employees = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $employees[] = $row;
    }
}

// Get existing attendance records for the selected date
$sql = "SELECT * FROM attendance WHERE date = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $date);
$stmt->execute();
$result = $stmt->get_result();
$attendance_records = [];
while ($row = $result->fetch_assoc()) {
    $attendance_records[$row['employee_id']] = $row;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $record_date = sanitize($_POST['date']);
        $record_type = sanitize($_POST['record_type']);
        
        // Single employee record
        if ($record_type === 'single' && isset($_POST['employee_id'])) {
            $emp_id = (int)$_POST['employee_id'];
            $time_in = !empty($_POST['time_in']) ? sanitize($_POST['time_in']) : null;
            $time_out = !empty($_POST['time_out']) ? sanitize($_POST['time_out']) : null;
            $status = sanitize($_POST['status']);
            $overtime_hours = !empty($_POST['overtime_hours']) ? (float)$_POST['overtime_hours'] : 0;
            $notes = sanitize($_POST['notes']);
            
            // Check if record already exists
            if (isset($attendance_records[$emp_id])) {
                // Update existing record
                $sql = "UPDATE attendance SET 
                        time_in = ?, 
                        time_out = ?, 
                        status = ?, 
                        overtime_hours = ?, 
                        notes = ? 
                        WHERE employee_id = ? AND date = ?";
                
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sssdssi", $time_in, $time_out, $status, $overtime_hours, $notes, $emp_id, $record_date);
                
                if ($stmt->execute()) {
                    $success[] = 'تم تحديث سجل الحضور للموظف بنجاح';
                    
                    // Log activity
                    $employee_name = '';
                    foreach ($employees as $emp) {
                        if ($emp['id'] == $emp_id) {
                            $employee_name = $emp['name'];
                            break;
                        }
                    }
                    log_activity('update_attendance', 'تم تحديث سجل حضور الموظف: ' . $employee_name . ' ليوم ' . format_date($record_date));
                } else {
                    $errors[] = 'حدث خطأ أثناء تحديث سجل الحضور';
                }
            } else {
                // Insert new record
                $sql = "INSERT INTO attendance (employee_id, date, time_in, time_out, status, overtime_hours, notes, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("issssds", $emp_id, $record_date, $time_in, $time_out, $status, $overtime_hours, $notes);
                
                if ($stmt->execute()) {
                    $success[] = 'تم تسجيل الحضور للموظف بنجاح';
                    
                    // Update attendance records array
                    $new_record = [
                        'id' => $conn->insert_id,
                        'employee_id' => $emp_id,
                        'date' => $record_date,
                        'time_in' => $time_in,
                        'time_out' => $time_out,
                        'status' => $status,
                        'overtime_hours' => $overtime_hours,
                        'notes' => $notes
                    ];
                    $attendance_records[$emp_id] = $new_record;
                    
                    // Log activity
                    $employee_name = '';
                    foreach ($employees as $emp) {
                        if ($emp['id'] == $emp_id) {
                            $employee_name = $emp['name'];
                            break;
                        }
                    }
                    log_activity('add_attendance', 'تم تسجيل حضور الموظف: ' . $employee_name . ' ليوم ' . format_date($record_date));
                } else {
                    $errors[] = 'حدث خطأ أثناء تسجيل الحضور';
                }
            }
        }
        // Bulk record
        elseif ($record_type === 'bulk' && isset($_POST['employee_ids'])) {
            $employee_ids = $_POST['employee_ids'];
            $statuses = $_POST['bulk_status'];
            
            if (empty($employee_ids)) {
                $errors[] = 'يرجى اختيار موظف واحد على الأقل';
            } else {
                $success_count = 0;
                $error_count = 0;
                
                foreach ($employee_ids as $index => $emp_id) {
                    $emp_id = (int)$emp_id;
                    $status = sanitize($statuses[$index]);
                    
                    // Check if record already exists
                    if (isset($attendance_records[$emp_id])) {
                        // Update existing record
                        $sql = "UPDATE attendance SET 
                                status = ? 
                                WHERE employee_id = ? AND date = ?";
                        
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("sis", $status, $emp_id, $record_date);
                        
                        if ($stmt->execute()) {
                            $success_count++;
                            
                            // Update attendance records array
                            $attendance_records[$emp_id]['status'] = $status;
                        } else {
                            $error_count++;
                        }
                    } else {
                        // Insert new record
                        $sql = "INSERT INTO attendance (employee_id, date, status, created_at) 
                                VALUES (?, ?, ?, NOW())";
                        
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("iss", $emp_id, $record_date, $status);
                        
                        if ($stmt->execute()) {
                            $success_count++;
                            
                            // Update attendance records array
                            $new_record = [
                                'id' => $conn->insert_id,
                                'employee_id' => $emp_id,
                                'date' => $record_date,
                                'time_in' => null,
                                'time_out' => null,
                                'status' => $status,
                                'overtime_hours' => 0,
                                'notes' => ''
                            ];
                            $attendance_records[$emp_id] = $new_record;
                        } else {
                            $error_count++;
                        }
                    }
                }
                
                if ($success_count > 0) {
                    $success[] = 'تم تسجيل الحضور لـ ' . $success_count . ' موظف بنجاح';
                    
                    // Log activity
                    log_activity('bulk_attendance', 'تم تسجيل حضور ' . $success_count . ' موظف ليوم ' . format_date($record_date));
                }
                
                if ($error_count > 0) {
                    $errors[] = 'حدث خطأ أثناء تسجيل الحضور لـ ' . $error_count . ' موظف';
                }
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">تسجيل الحضور</h1>
    <a href="index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
        <i class="fas fa-arrow-right ml-2"></i> العودة إلى سجل الحضور
    </a>
</div>

<!-- Error Messages -->
<?php if (!empty($errors)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">خطأ!</strong>
        <ul class="mt-1 list-disc list-inside">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Success Messages -->
<?php if (!empty($success)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">تم بنجاح!</strong>
        <ul class="mt-1 list-disc list-inside">
            <?php foreach ($success as $msg): ?>
                <li><?php echo $msg; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Date Selection -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="flex items-center">
        <div class="ml-4">
            <label for="date" class="block text-sm font-medium text-gray-700 mb-1">التاريخ</label>
            <input type="date" id="date" name="date" value="<?php echo $date; ?>" class="border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
        </div>
        
        <?php if ($employee_id > 0): ?>
            <input type="hidden" name="employee_id" value="<?php echo $employee_id; ?>">
        <?php endif; ?>
        
        <div class="mt-6">
            <button type="submit" class="bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-calendar-day ml-2"></i> عرض
            </button>
        </div>
    </form>
</div>

<!-- Attendance Forms -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Single Employee Attendance -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-primary text-white px-6 py-4">
            <h2 class="text-xl font-semibold">تسجيل حضور موظف واحد</h2>
        </div>
        <div class="p-6">
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?date=' . $date); ?>">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="date" value="<?php echo $date; ?>">
                <input type="hidden" name="record_type" value="single">
                
                <div class="mb-4">
                    <label for="employee_id" class="block text-sm font-medium text-gray-700 mb-1">الموظف <span class="text-red-500">*</span></label>
                    <select id="employee_id" name="employee_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                        <option value="">اختر الموظف</option>
                        <?php foreach ($employees as $emp): ?>
                            <option value="<?php echo $emp['id']; ?>" <?php echo $employee_id == $emp['id'] ? 'selected' : ''; ?>>
                                <?php echo $emp['name'] . ' (' . $emp['employee_number'] . ')'; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="time_in" class="block text-sm font-medium text-gray-700 mb-1">وقت الحضور</label>
                        <input type="time" id="time_in" name="time_in" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="time_out" class="block text-sm font-medium text-gray-700 mb-1">وقت الانصراف</label>
                        <input type="time" id="time_out" name="time_out" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة <span class="text-red-500">*</span></label>
                        <select id="status" name="status" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="leave">إجازة</option>
                            <option value="late">متأخر</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="overtime_hours" class="block text-sm font-medium text-gray-700 mb-1">ساعات العمل الإضافية</label>
                        <input type="number" id="overtime_hours" name="overtime_hours" step="0.5" min="0" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="2" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                        <i class="fas fa-save ml-2"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Bulk Attendance -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-secondary text-white px-6 py-4">
            <h2 class="text-xl font-semibold">تسجيل حضور جماعي</h2>
        </div>
        <div class="p-6">
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?date=' . $date); ?>">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="date" value="<?php echo $date; ?>">
                <input type="hidden" name="record_type" value="bulk">
                
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-700">الموظفين</label>
                        <div>
                            <button type="button" id="selectAll" class="text-xs text-primary hover:underline">اختيار الكل</button>
                            <span class="text-gray-300 mx-1">|</span>
                            <button type="button" id="deselectAll" class="text-xs text-primary hover:underline">إلغاء اختيار الكل</button>
                        </div>
                    </div>
                    
                    <div class="border border-gray-300 rounded-md p-4 max-h-96 overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        اختيار
                                    </th>
                                    <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الموظف
                                    </th>
                                    <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        القسم
                                    </th>
                                    <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($employees as $index => $emp): ?>
                                    <tr>
                                        <td class="px-3 py-2 whitespace-nowrap">
                                            <input type="checkbox" name="employee_ids[]" value="<?php echo $emp['id']; ?>" class="employee-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo $emp['name']; ?>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                <?php echo $emp['employee_number']; ?>
                                            </div>
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo $emp['department']; ?>
                                        </td>
                                        <td class="px-3 py-2 whitespace-nowrap">
                                            <select name="bulk_status[]" class="text-sm border border-gray-300 rounded-md py-1 px-2 focus:outline-none focus:ring-primary focus:border-primary">
                                                <option value="present" <?php echo isset($attendance_records[$emp['id']]) && $attendance_records[$emp['id']]['status'] === 'present' ? 'selected' : ''; ?>>حاضر</option>
                                                <option value="absent" <?php echo isset($attendance_records[$emp['id']]) && $attendance_records[$emp['id']]['status'] === 'absent' ? 'selected' : ''; ?>>غائب</option>
                                                <option value="leave" <?php echo isset($attendance_records[$emp['id']]) && $attendance_records[$emp['id']]['status'] === 'leave' ? 'selected' : ''; ?>>إجازة</option>
                                                <option value="late" <?php echo isset($attendance_records[$emp['id']]) && $attendance_records[$emp['id']]['status'] === 'late' ? 'selected' : ''; ?>>متأخر</option>
                                            </select>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                        <i class="fas fa-save ml-2"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attendance Summary -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
    <div class="bg-blue-600 text-white px-6 py-4">
        <h2 class="text-xl font-semibold">ملخص الحضور ليوم <?php echo format_date($date); ?></h2>
    </div>
    <div class="p-6">
        <?php
        $summary = [
            'total' => count($employees),
            'present' => 0,
            'absent' => 0,
            'leave' => 0,
            'late' => 0,
            'not_recorded' => 0
        ];
        
        foreach ($employees as $emp) {
            if (isset($attendance_records[$emp['id']])) {
                $summary[$attendance_records[$emp['id']]['status']]++;
            } else {
                $summary['not_recorded']++;
            }
        }
        ?>
        
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-gray-600 text-2xl font-bold"><?php echo $summary['total']; ?></p>
                <p class="text-sm text-gray-500">إجمالي الموظفين</p>
            </div>
            <div class="bg-green-50 p-4 rounded-lg text-center">
                <p class="text-green-600 text-2xl font-bold"><?php echo $summary['present']; ?></p>
                <p class="text-sm text-gray-500">حاضر</p>
            </div>
            <div class="bg-red-50 p-4 rounded-lg text-center">
                <p class="text-red-600 text-2xl font-bold"><?php echo $summary['absent']; ?></p>
                <p class="text-sm text-gray-500">غائب</p>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg text-center">
                <p class="text-purple-600 text-2xl font-bold"><?php echo $summary['leave']; ?></p>
                <p class="text-sm text-gray-500">إجازة</p>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg text-center">
                <p class="text-yellow-600 text-2xl font-bold"><?php echo $summary['late']; ?></p>
                <p class="text-sm text-gray-500">متأخر</p>
            </div>
        </div>
        
        <?php if ($summary['not_recorded'] > 0): ?>
            <div class="mt-4 text-center text-yellow-600">
                <p>هناك <?php echo $summary['not_recorded']; ?> موظف لم يتم تسجيل حضورهم بعد</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select/Deselect All
        const selectAllBtn = document.getElementById('selectAll');
        const deselectAllBtn = document.getElementById('deselectAll');
        const employeeCheckboxes = document.querySelectorAll('.employee-checkbox');
        
        selectAllBtn.addEventListener('click', function() {
            employeeCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        });
        
        deselectAllBtn.addEventListener('click', function() {
            employeeCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });
        
        // Pre-fill form if employee_id is provided
        const employeeSelect = document.getElementById('employee_id');
        if (employeeSelect.value) {
            <?php if ($employee_id > 0 && isset($attendance_records[$employee_id])): ?>
                const record = <?php echo json_encode($attendance_records[$employee_id]); ?>;
                
                if (record.time_in) {
                    document.getElementById('time_in').value = record.time_in.substring(0, 5);
                }
                
                if (record.time_out) {
                    document.getElementById('time_out').value = record.time_out.substring(0, 5);
                }
                
                document.getElementById('status').value = record.status;
                document.getElementById('overtime_hours').value = record.overtime_hours;
                document.getElementById('notes').value = record.notes;
            <?php endif; ?>
        }
    });
</script>

<?php
// Include footer
require_once '../includes/footer.php';
?>
