@echo off
echo ========================================
echo    نظام إدارة الموظفين والرواتب
echo    خادم التطوير المحلي
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: PHP غير مثبت أو غير متاح في PATH
    echo.
    echo يرجى تثبيت PHP أو استخدام XAMPP/WAMP
    echo راجع ملف local-setup.md للتفاصيل
    pause
    exit /b 1
)

echo ✅ تم العثور على PHP
php --version
echo.

REM التحقق من وجود المجلد الحالي
if not exist "index.php" (
    echo ❌ خطأ: لم يتم العثور على ملفات المشروع
    echo تأكد من تشغيل هذا الملف من مجلد المشروع
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات المشروع
echo.

REM إنشاء مجلدات الرفع إذا لم تكن موجودة
if not exist "assets\uploads" mkdir "assets\uploads"
if not exist "assets\uploads\employees" mkdir "assets\uploads\employees"
if not exist "backup" mkdir "backup"

echo ✅ تم إنشاء المجلدات المطلوبة
echo.

echo 🚀 بدء تشغيل خادم PHP المحلي...
echo.
echo 📍 عنوان الموقع: http://localhost:8000
echo 📍 فحص المتطلبات: http://localhost:8000/check-requirements.php
echo 📍 معالج التثبيت: http://localhost:8000/install.php
echo.
echo ⚠️  ملاحظة: ستحتاج إلى إعداد قاعدة بيانات MySQL منفصلة
echo    راجع ملف local-setup.md للتفاصيل
echo.
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo.

REM بدء الخادم
php -S localhost:8000
