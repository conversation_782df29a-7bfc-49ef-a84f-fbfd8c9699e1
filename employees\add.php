<?php
/**
 * Add Employee Page
 * 
 * This file handles adding a new employee
 */

// Include header
require_once '../includes/header.php';

// Initialize variables
$employee = [
    'employee_number' => '',
    'name' => '',
    'position' => '',
    'department' => '',
    'join_date' => date('Y-m-d'),
    'basic_salary' => '',
    'phone' => '',
    'email' => '',
    'status' => 'active',
    'notes' => ''
];
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $employee['employee_number'] = sanitize($_POST['employee_number']);
        $employee['name'] = sanitize($_POST['name']);
        $employee['position'] = sanitize($_POST['position']);
        $employee['department'] = sanitize($_POST['department']);
        $employee['join_date'] = sanitize($_POST['join_date']);
        $employee['basic_salary'] = sanitize($_POST['basic_salary']);
        $employee['phone'] = sanitize($_POST['phone']);
        $employee['email'] = sanitize($_POST['email']);
        $employee['status'] = sanitize($_POST['status']);
        $employee['notes'] = sanitize($_POST['notes']);
        
        // Validate input
        if (empty($employee['employee_number'])) {
            $errors['employee_number'] = 'رقم الموظف مطلوب';
        } else {
            // Check if employee number already exists
            $sql = "SELECT id FROM employees WHERE employee_number = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $employee['employee_number']);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $errors['employee_number'] = 'رقم الموظف موجود بالفعل';
            }
        }
        
        if (empty($employee['name'])) {
            $errors['name'] = 'اسم الموظف مطلوب';
        }
        
        if (empty($employee['position'])) {
            $errors['position'] = 'المسمى الوظيفي مطلوب';
        }
        
        if (empty($employee['department'])) {
            $errors['department'] = 'القسم مطلوب';
        }
        
        if (empty($employee['join_date'])) {
            $errors['join_date'] = 'تاريخ التعيين مطلوب';
        } else {
            $date = DateTime::createFromFormat('Y-m-d', $employee['join_date']);
            if (!$date || $date->format('Y-m-d') !== $employee['join_date']) {
                $errors['join_date'] = 'تاريخ التعيين غير صالح';
            }
        }
        
        if (empty($employee['basic_salary'])) {
            $errors['basic_salary'] = 'الراتب الأساسي مطلوب';
        } elseif (!is_numeric($employee['basic_salary']) || $employee['basic_salary'] <= 0) {
            $errors['basic_salary'] = 'الراتب الأساسي يجب أن يكون رقماً موجباً';
        }
        
        if (!empty($employee['email']) && !filter_var($employee['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'البريد الإلكتروني غير صالح';
        }
        
        // Handle image upload
        $image_name = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB
            
            if (!in_array($_FILES['image']['type'], $allowed_types)) {
                $errors['image'] = 'نوع الملف غير مدعوم. يرجى استخدام JPEG أو PNG أو GIF';
            } elseif ($_FILES['image']['size'] > $max_size) {
                $errors['image'] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
            } else {
                // Generate unique filename
                $image_name = time() . '_' . basename($_FILES['image']['name']);
                $upload_path = EMPLOYEE_IMAGES . $image_name;
                
                // Create directory if it doesn't exist
                if (!file_exists(EMPLOYEE_IMAGES)) {
                    mkdir(EMPLOYEE_IMAGES, 0755, true);
                }
                
                // Move uploaded file
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $errors['image'] = 'فشل في تحميل الصورة. يرجى المحاولة مرة أخرى';
                }
            }
        }
        
        // If no errors, insert employee
        if (empty($errors)) {
            $sql = "INSERT INTO employees (employee_number, name, position, department, join_date, basic_salary, phone, email, status, notes, image, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssdsssss", 
                $employee['employee_number'], 
                $employee['name'], 
                $employee['position'], 
                $employee['department'], 
                $employee['join_date'], 
                $employee['basic_salary'], 
                $employee['phone'], 
                $employee['email'], 
                $employee['status'], 
                $employee['notes'], 
                $image_name
            );
            
            if ($stmt->execute()) {
                $employee_id = $conn->insert_id;
                
                // Log activity
                log_activity('add_employee', 'تمت إضافة موظف جديد: ' . $employee['name']);
                
                // Set success message
                set_flash_message('تمت إضافة الموظف بنجاح', 'success');
                
                // Redirect to employee details
                redirect("view.php?id=$employee_id");
            } else {
                $errors['db'] = 'حدث خطأ أثناء إضافة الموظف. يرجى المحاولة مرة أخرى';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">إضافة موظف جديد</h1>
    <a href="index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
        <i class="fas fa-arrow-right ml-2"></i> العودة إلى قائمة الموظفين
    </a>
</div>

<!-- Error Messages -->
<?php if (isset($errors['db']) || isset($errors['csrf'])): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">خطأ!</strong>
        <span class="block sm:inline"><?php echo $errors['db'] ?? $errors['csrf']; ?></span>
    </div>
<?php endif; ?>

<!-- Employee Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Employee Number -->
            <div>
                <label for="employee_number" class="block text-sm font-medium text-gray-700 mb-1">رقم الموظف <span class="text-red-500">*</span></label>
                <input type="text" id="employee_number" name="employee_number" value="<?php echo $employee['employee_number']; ?>" class="w-full border <?php echo isset($errors['employee_number']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['employee_number'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['employee_number']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل <span class="text-red-500">*</span></label>
                <input type="text" id="name" name="name" value="<?php echo $employee['name']; ?>" class="w-full border <?php echo isset($errors['name']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['name'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['name']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Position -->
            <div>
                <label for="position" class="block text-sm font-medium text-gray-700 mb-1">المسمى الوظيفي <span class="text-red-500">*</span></label>
                <input type="text" id="position" name="position" value="<?php echo $employee['position']; ?>" class="w-full border <?php echo isset($errors['position']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['position'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['position']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Department -->
            <div>
                <label for="department" class="block text-sm font-medium text-gray-700 mb-1">القسم <span class="text-red-500">*</span></label>
                <input type="text" id="department" name="department" value="<?php echo $employee['department']; ?>" class="w-full border <?php echo isset($errors['department']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['department'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['department']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Join Date -->
            <div>
                <label for="join_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ التعيين <span class="text-red-500">*</span></label>
                <input type="date" id="join_date" name="join_date" value="<?php echo $employee['join_date']; ?>" class="w-full border <?php echo isset($errors['join_date']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['join_date'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['join_date']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Basic Salary -->
            <div>
                <label for="basic_salary" class="block text-sm font-medium text-gray-700 mb-1">الراتب الأساسي <span class="text-red-500">*</span></label>
                <input type="number" id="basic_salary" name="basic_salary" value="<?php echo $employee['basic_salary']; ?>" step="0.01" min="0" class="w-full border <?php echo isset($errors['basic_salary']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['basic_salary'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['basic_salary']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Phone -->
            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                <input type="tel" id="phone" name="phone" value="<?php echo $employee['phone']; ?>" class="w-full border <?php echo isset($errors['phone']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <?php if (isset($errors['phone'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['phone']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Email -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" value="<?php echo $employee['email']; ?>" class="w-full border <?php echo isset($errors['email']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <?php if (isset($errors['email'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['email']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select id="status" name="status" class="w-full border <?php echo isset($errors['status']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                    <option value="active" <?php echo $employee['status'] === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="inactive" <?php echo $employee['status'] === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                </select>
                <?php if (isset($errors['status'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['status']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Image -->
            <div>
                <label for="image" class="block text-sm font-medium text-gray-700 mb-1">صورة الموظف</label>
                <input type="file" id="image" name="image" accept="image/*" class="w-full border <?php echo isset($errors['image']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <p class="mt-1 text-xs text-gray-500">الصيغ المدعومة: JPEG, PNG, GIF. الحد الأقصى للحجم: 2MB</p>
                <?php if (isset($errors['image'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['image']; ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="mt-6">
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
            <textarea id="notes" name="notes" rows="4" class="w-full border <?php echo isset($errors['notes']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"><?php echo $employee['notes']; ?></textarea>
            <?php if (isset($errors['notes'])): ?>
                <p class="mt-1 text-sm text-red-500"><?php echo $errors['notes']; ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Submit Button -->
        <div class="mt-6 flex justify-end">
            <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-save ml-2"></i> حفظ الموظف
            </button>
        </div>
    </form>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
