<?php
/**
 * Delete Attendance Record Page
 * 
 * This file handles deleting an attendance record
 */

// Include functions file
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('معرّف سجل الحضور غير صالح', 'danger');
    redirect('index.php');
}

// Get attendance record ID
$attendance_id = (int)$_GET['id'];

// Get attendance record details
$sql = "SELECT a.*, e.name as employee_name 
        FROM attendance a 
        JOIN employees e ON a.employee_id = e.id 
        WHERE a.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $attendance_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if record exists
if ($result->num_rows === 0) {
    set_flash_message('سجل الحضور غير موجود', 'danger');
    redirect('index.php');
}

// Get attendance data
$attendance = $result->fetch_assoc();

// Delete attendance record
$sql = "DELETE FROM attendance WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $attendance_id);

if ($stmt->execute()) {
    // Log activity
    log_activity('delete_attendance', 'تم حذف سجل حضور الموظف: ' . $attendance['employee_name'] . ' ليوم ' . format_date($attendance['date']));
    
    // Set success message
    set_flash_message('تم حذف سجل الحضور بنجاح', 'success');
} else {
    // Set error message
    set_flash_message('حدث خطأ أثناء حذف سجل الحضور', 'danger');
}

// Redirect to attendance list
redirect('index.php');
?>
