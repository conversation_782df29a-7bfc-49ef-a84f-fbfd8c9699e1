<?php
/**
 * Header File
 * 
 * This file contains the header section of the Employee Management System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include functions file
require_once 'includes/functions.php';

// Check if user is logged in
if (!is_logged_in() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    redirect('login.php');
}

// Get current page
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#FF6B00', // Orange
                            dark: '#E05A00',
                            light: '#FF8C3F'
                        },
                        secondary: {
                            DEFAULT: '#1F2937', // Dark Gray
                            dark: '#111827',
                            light: '#374151'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo (Arabic) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        /* RTL specific adjustments */
        .rtl-flip {
            transform: scaleX(-1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <?php if (is_logged_in() && basename($_SERVER['PHP_SELF']) !== 'login.php'): ?>
    <!-- Navigation -->
    <nav class="bg-secondary text-white shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center">
                    <a href="index.php" class="text-xl font-bold text-primary">
                        <?php echo SYSTEM_NAME; ?>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="relative">
                        <button id="userMenuButton" class="flex items-center space-x-2 space-x-reverse focus:outline-none">
                            <span class="text-sm"><?php echo $_SESSION['user_name'] ?? 'المستخدم'; ?></span>
                            <i class="fas fa-user-circle text-xl"></i>
                        </button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden z-10">
                            <a href="settings/profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog ml-2"></i> الملف الشخصي
                            </a>
                            <a href="logout.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Sidebar and Content -->
    <div class="container mx-auto px-4 py-8 flex flex-wrap">
        <!-- Sidebar -->
        <aside class="w-full md:w-1/5 bg-white rounded-lg shadow-md p-4 ml-6">
            <ul class="space-y-2">
                <li>
                    <a href="index.php" class="block py-2 px-4 rounded-md <?php echo $current_page === 'index' ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-tachometer-alt ml-2"></i> لوحة التحكم
                    </a>
                </li>
                <li>
                    <a href="employees/index.php" class="block py-2 px-4 rounded-md <?php echo strpos($current_page, 'employees') !== false ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-users ml-2"></i> الموظفين
                    </a>
                </li>
                <li>
                    <a href="attendance/index.php" class="block py-2 px-4 rounded-md <?php echo strpos($current_page, 'attendance') !== false ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-calendar-check ml-2"></i> الحضور
                    </a>
                </li>
                <li>
                    <a href="salaries/index.php" class="block py-2 px-4 rounded-md <?php echo strpos($current_page, 'salaries') !== false ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-money-bill-wave ml-2"></i> المرتبات
                    </a>
                </li>
                <li>
                    <a href="reports/index.php" class="block py-2 px-4 rounded-md <?php echo strpos($current_page, 'reports') !== false ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-chart-bar ml-2"></i> التقارير
                    </a>
                </li>
                <li>
                    <a href="settings/index.php" class="block py-2 px-4 rounded-md <?php echo strpos($current_page, 'settings') !== false ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <i class="fas fa-cog ml-2"></i> الإعدادات
                    </a>
                </li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="w-full md:w-4/5 bg-white rounded-lg shadow-md p-6">
            <?php echo display_flash_message(); ?>
    <?php endif; ?>
