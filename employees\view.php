<?php
/**
 * View Employee Page
 * 
 * This file displays employee details
 */

// Include header
require_once '../includes/header.php';

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('معرّف الموظف غير صالح', 'danger');
    redirect('index.php');
}

// Get employee ID
$employee_id = (int)$_GET['id'];

// Get employee details
$sql = "SELECT * FROM employees WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $employee_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if employee exists
if ($result->num_rows === 0) {
    set_flash_message('الموظف غير موجود', 'danger');
    redirect('index.php');
}

// Get employee data
$employee = $result->fetch_assoc();

// Get attendance statistics
$stats = [
    'present' => 0,
    'absent' => 0,
    'leave' => 0,
    'late' => 0,
    'total_days' => 0
];

// Current month and year
$current_month = date('m');
$current_year = date('Y');

// Get attendance for current month
$sql = "SELECT status, COUNT(*) as count 
        FROM attendance 
        WHERE employee_id = ? 
        AND MONTH(date) = ? 
        AND YEAR(date) = ? 
        GROUP BY status";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iss", $employee_id, $current_month, $current_year);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $stats[$row['status']] = $row['count'];
    $stats['total_days'] += $row['count'];
}

// Get recent attendance
$sql = "SELECT * FROM attendance 
        WHERE employee_id = ? 
        ORDER BY date DESC 
        LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $employee_id);
$stmt->execute();
$result = $stmt->get_result();
$recent_attendance = [];
while ($row = $result->fetch_assoc()) {
    $recent_attendance[] = $row;
}

// Get recent salaries
$sql = "SELECT * FROM salaries 
        WHERE employee_id = ? 
        ORDER BY year DESC, month DESC 
        LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $employee_id);
$stmt->execute();
$result = $stmt->get_result();
$recent_salaries = [];
while ($row = $result->fetch_assoc()) {
    $recent_salaries[] = $row;
}
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">بيانات الموظف</h1>
    <div class="flex space-x-2 space-x-reverse">
        <a href="index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى قائمة الموظفين
        </a>
        <a href="edit.php?id=<?php echo $employee_id; ?>" class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-edit ml-2"></i> تعديل
        </a>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Employee Information -->
    <div class="md:col-span-2">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-secondary text-white px-6 py-4">
                <h2 class="text-xl font-semibold">معلومات الموظف</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">رقم الموظف</p>
                        <p class="font-medium"><?php echo $employee['employee_number']; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الاسم الكامل</p>
                        <p class="font-medium"><?php echo $employee['name']; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">المسمى الوظيفي</p>
                        <p class="font-medium"><?php echo $employee['position']; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">القسم</p>
                        <p class="font-medium"><?php echo $employee['department']; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">تاريخ التعيين</p>
                        <p class="font-medium"><?php echo format_date($employee['join_date']); ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الراتب الأساسي</p>
                        <p class="font-medium"><?php echo format_currency($employee['basic_salary']); ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">رقم الهاتف</p>
                        <p class="font-medium"><?php echo $employee['phone'] ?: 'غير متوفر'; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">البريد الإلكتروني</p>
                        <p class="font-medium"><?php echo $employee['email'] ?: 'غير متوفر'; ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الحالة</p>
                        <p class="font-medium">
                            <?php if ($employee['status'] === 'active'): ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    نشط
                                </span>
                            <?php else: ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    غير نشط
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">تاريخ الإضافة</p>
                        <p class="font-medium"><?php echo format_date($employee['created_at'], 'Y-m-d H:i'); ?></p>
                    </div>
                </div>
                
                <?php if (!empty($employee['notes'])): ?>
                    <div class="mt-6">
                        <p class="text-sm text-gray-500 mb-1">ملاحظات</p>
                        <p class="font-medium"><?php echo nl2br($employee['notes']); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Attendance Statistics -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
            <div class="bg-primary text-white px-6 py-4">
                <h2 class="text-xl font-semibold">إحصائيات الحضور (الشهر الحالي)</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <p class="text-green-600 text-2xl font-bold"><?php echo $stats['present']; ?></p>
                        <p class="text-sm text-gray-500">حاضر</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg text-center">
                        <p class="text-red-600 text-2xl font-bold"><?php echo $stats['absent']; ?></p>
                        <p class="text-sm text-gray-500">غائب</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg text-center">
                        <p class="text-purple-600 text-2xl font-bold"><?php echo $stats['leave']; ?></p>
                        <p class="text-sm text-gray-500">إجازة</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <p class="text-yellow-600 text-2xl font-bold"><?php echo $stats['late']; ?></p>
                        <p class="text-sm text-gray-500">متأخر</p>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="../attendance/index.php?employee_id=<?php echo $employee_id; ?>" class="text-primary hover:underline">
                        عرض سجل الحضور الكامل
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Recent Attendance -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
            <div class="bg-blue-600 text-white px-6 py-4">
                <h2 class="text-xl font-semibold">آخر سجلات الحضور</h2>
            </div>
            <div class="p-6">
                <?php if (empty($recent_attendance)): ?>
                    <p class="text-gray-500 text-center py-4">لا توجد سجلات حضور</p>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التاريخ
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        وقت الحضور
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        وقت الانصراف
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recent_attendance as $attendance): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo format_date($attendance['date']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo $attendance['time_in'] ? format_time($attendance['time_in']) : '-'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo $attendance['time_out'] ? format_time($attendance['time_out']) : '-'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            
                                            switch ($attendance['status']) {
                                                case 'present':
                                                    $status_class = 'bg-green-100 text-green-800';
                                                    $status_text = 'حاضر';
                                                    break;
                                                case 'absent':
                                                    $status_class = 'bg-red-100 text-red-800';
                                                    $status_text = 'غائب';
                                                    break;
                                                case 'leave':
                                                    $status_class = 'bg-purple-100 text-purple-800';
                                                    $status_text = 'إجازة';
                                                    break;
                                                case 'late':
                                                    $status_class = 'bg-yellow-100 text-yellow-800';
                                                    $status_text = 'متأخر';
                                                    break;
                                            }
                                            ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Salaries -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
            <div class="bg-green-600 text-white px-6 py-4">
                <h2 class="text-xl font-semibold">آخر المرتبات</h2>
            </div>
            <div class="p-6">
                <?php if (empty($recent_salaries)): ?>
                    <p class="text-gray-500 text-center py-4">لا توجد سجلات مرتبات</p>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الشهر/السنة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الراتب الأساسي
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        البدلات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الخصومات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الإجمالي
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recent_salaries as $salary): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php 
                                            $month_names = [
                                                1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
                                                5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
                                                9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
                                            ];
                                            echo $month_names[$salary['month']] . ' ' . $salary['year']; 
                                            ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo format_currency($salary['basic_salary']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo format_currency($salary['allowances']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo format_currency($salary['deductions']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo format_currency($salary['total_salary']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($salary['status'] === 'paid'): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    مدفوع
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    معلق
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 text-center">
                        <a href="../salaries/index.php?employee_id=<?php echo $employee_id; ?>" class="text-primary hover:underline">
                            عرض سجل المرتبات الكامل
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Employee Profile -->
    <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 text-center">
                <?php if (!empty($employee['image'])): ?>
                    <img src="../assets/uploads/employees/<?php echo $employee['image']; ?>" alt="<?php echo $employee['name']; ?>" class="w-32 h-32 rounded-full mx-auto mb-4 object-cover">
                <?php else: ?>
                    <div class="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-gray-400 text-5xl"></i>
                    </div>
                <?php endif; ?>
                
                <h3 class="text-xl font-bold mb-1"><?php echo $employee['name']; ?></h3>
                <p class="text-gray-500 mb-4"><?php echo $employee['position']; ?></p>
                
                <div class="flex justify-center space-x-2 space-x-reverse">
                    <?php if (!empty($employee['phone'])): ?>
                        <a href="tel:<?php echo $employee['phone']; ?>" class="bg-blue-100 text-blue-600 p-2 rounded-full">
                            <i class="fas fa-phone"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($employee['email'])): ?>
                        <a href="mailto:<?php echo $employee['email']; ?>" class="bg-green-100 text-green-600 p-2 rounded-full">
                            <i class="fas fa-envelope"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="border-t px-6 py-4">
                <h4 class="font-semibold mb-2">روابط سريعة</h4>
                <ul class="space-y-2">
                    <li>
                        <a href="../attendance/record.php?employee_id=<?php echo $employee_id; ?>" class="flex items-center text-primary hover:underline">
                            <i class="fas fa-clipboard-check ml-2"></i> تسجيل الحضور
                        </a>
                    </li>
                    <li>
                        <a href="../salaries/calculate.php?employee_id=<?php echo $employee_id; ?>" class="flex items-center text-primary hover:underline">
                            <i class="fas fa-calculator ml-2"></i> حساب الراتب
                        </a>
                    </li>
                    <li>
                        <a href="../reports/employee.php?id=<?php echo $employee_id; ?>" class="flex items-center text-primary hover:underline">
                            <i class="fas fa-chart-bar ml-2"></i> تقرير الموظف
                        </a>
                    </li>
                    <li>
                        <a href="edit.php?id=<?php echo $employee_id; ?>" class="flex items-center text-primary hover:underline">
                            <i class="fas fa-edit ml-2"></i> تعديل البيانات
                        </a>
                    </li>
                    <li>
                        <a href="delete.php?id=<?php echo $employee_id; ?>" class="flex items-center text-red-600 hover:underline" onclick="return confirm('هل أنت متأكد من حذف هذا الموظف؟');">
                            <i class="fas fa-trash-alt ml-2"></i> حذف الموظف
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
