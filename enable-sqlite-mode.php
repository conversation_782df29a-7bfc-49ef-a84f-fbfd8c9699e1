<?php
/**
 * Enable SQLite Mode for Local Development
 * 
 * This script enables SQLite mode for quick local testing without MySQL
 */

$flag_file = 'includes/use_sqlite.flag';
$action = $_GET['action'] ?? '';

if ($action === 'enable') {
    // Create flag file to enable SQLite mode
    file_put_contents($flag_file, date('Y-m-d H:i:s'));
    $message = '✅ تم تفعيل وضع SQLite للتطوير المحلي';
    $status = 'success';
} elseif ($action === 'disable') {
    // Remove flag file to disable SQLite mode
    if (file_exists($flag_file)) {
        unlink($flag_file);
    }
    $message = '✅ تم إلغاء تفعيل وضع SQLite';
    $status = 'success';
}

$sqlite_enabled = file_exists($flag_file);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد وضع التطوير المحلي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                <i class="fas fa-cog text-blue-600 ml-2"></i>
                إعداد وضع التطوير المحلي
            </h1>
            
            <?php if (isset($message)): ?>
                <div class="bg-<?php echo $status === 'success' ? 'green' : 'red'; ?>-100 border border-<?php echo $status === 'success' ? 'green' : 'red'; ?>-400 text-<?php echo $status === 'success' ? 'green' : 'red'; ?>-700 px-4 py-3 rounded mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <div class="mb-6">
                <h2 class="text-xl font-bold mb-4">الحالة الحالية:</h2>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <?php if ($sqlite_enabled): ?>
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-check-circle text-xl ml-2"></i>
                            <span class="font-bold">وضع SQLite مفعل</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">
                            النظام يستخدم قاعدة بيانات SQLite للتطوير المحلي
                        </p>
                    <?php else: ?>
                        <div class="flex items-center text-red-600">
                            <i class="fas fa-times-circle text-xl ml-2"></i>
                            <span class="font-bold">وضع SQLite غير مفعل</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">
                            النظام يحاول الاتصال بقاعدة بيانات MySQL
                        </p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="font-bold text-lg mb-3">ما هو وضع SQLite؟</h3>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <ul class="list-disc list-inside text-sm space-y-2">
                        <li>يسمح بتشغيل النظام بدون تثبيت MySQL</li>
                        <li>يستخدم قاعدة بيانات SQLite محلية</li>
                        <li>مثالي للتطوير والاختبار السريع</li>
                        <li>لا يحتاج إعداد خادم قاعدة بيانات</li>
                        <li>البيانات تُحفظ في ملف محلي</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="font-bold text-lg mb-3">متى تستخدم كل وضع؟</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-database ml-1"></i>
                            وضع SQLite
                        </h4>
                        <ul class="text-sm text-green-700 space-y-1">
                            <li>• اختبار سريع</li>
                            <li>• لا يوجد MySQL</li>
                            <li>• تطوير محلي</li>
                            <li>• عرض توضيحي</li>
                        </ul>
                    </div>
                    
                    <div class="bg-orange-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-orange-800 mb-2">
                            <i class="fas fa-server ml-1"></i>
                            وضع MySQL
                        </h4>
                        <ul class="text-sm text-orange-700 space-y-1">
                            <li>• اختبار كامل</li>
                            <li>• بيئة مشابهة للإنتاج</li>
                            <li>• أداء أفضل</li>
                            <li>• ميزات متقدمة</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <?php if ($sqlite_enabled): ?>
                    <a href="?action=disable" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء تفعيل SQLite
                    </a>
                    
                    <a href="index.php" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-play ml-2"></i>
                        تشغيل النظام
                    </a>
                <?php else: ?>
                    <a href="?action=enable" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                        <i class="fas fa-check ml-2"></i>
                        تفعيل وضع SQLite
                    </a>
                    
                    <a href="check-requirements.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-search ml-2"></i>
                        فحص المتطلبات
                    </a>
                <?php endif; ?>
            </div>
            
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="font-bold text-yellow-800 mb-2">
                    <i class="fas fa-exclamation-triangle ml-1"></i>
                    ملاحظات مهمة:
                </h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• وضع SQLite للتطوير المحلي فقط</li>
                    <li>• استخدم MySQL للإنتاج على Hostinger</li>
                    <li>• البيانات في SQLite منفصلة عن MySQL</li>
                    <li>• يمكن التبديل بين الوضعين في أي وقت</li>
                </ul>
            </div>
            
            <?php if ($sqlite_enabled): ?>
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">
                        ملف قاعدة البيانات: <code>data/payroll_local.db</code>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
