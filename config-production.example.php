<?php
/**
 * Production Configuration Example
 * 
 * Copy this file to config-production.php and update with your actual values
 * This file contains production-specific settings for Hostinger hosting
 */

// Production Database Settings for Hostinger
define('PROD_DB_HOST', 'localhost');
define('PROD_DB_USER', 'u123456789_payroll');     // Replace with your actual database username
define('PROD_DB_PASS', 'YourSecurePassword123!'); // Replace with your actual database password  
define('PROD_DB_NAME', 'u123456789_payroll');     // Replace with your actual database name

// Production Security Settings
define('PROD_ENVIRONMENT', true);
define('PROD_DEBUG_MODE', false);
define('PROD_ERROR_REPORTING', false);

// Production Email Settings (for notifications)
define('PROD_SMTP_HOST', 'smtp.hostinger.com');
define('PROD_SMTP_PORT', 587);
define('PROD_SMTP_USERNAME', '<EMAIL>'); // Replace with your email
define('PROD_SMTP_PASSWORD', 'your_email_password');    // Replace with your email password
define('PROD_SMTP_FROM_EMAIL', '<EMAIL>');
define('PROD_SMTP_FROM_NAME', 'نظام إدارة الموظفين');

// Production File Upload Settings
define('PROD_MAX_UPLOAD_SIZE', 5242880); // 5MB
define('PROD_ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('PROD_UPLOAD_PATH', '/home/<USER>/domains/yourdomain.com/public_html/assets/uploads/');

// Production Security Keys (Generate new ones for production)
define('PROD_ENCRYPTION_KEY', 'your-32-character-encryption-key-here');
define('PROD_CSRF_SECRET', 'your-csrf-secret-key-here');

// Production Cache Settings
define('PROD_CACHE_ENABLED', true);
define('PROD_CACHE_DURATION', 3600); // 1 hour

// Production Backup Settings
define('PROD_BACKUP_ENABLED', true);
define('PROD_BACKUP_PATH', '/home/<USER>/domains/yourdomain.com/backups/');
define('PROD_BACKUP_RETENTION_DAYS', 30);

// Production Logging Settings
define('PROD_LOG_ENABLED', true);
define('PROD_LOG_PATH', '/home/<USER>/domains/yourdomain.com/logs/');
define('PROD_LOG_LEVEL', 'ERROR'); // DEBUG, INFO, WARNING, ERROR

// Production Performance Settings
define('PROD_GZIP_ENABLED', true);
define('PROD_MINIFY_CSS', true);
define('PROD_MINIFY_JS', true);

// Production Rate Limiting
define('PROD_RATE_LIMIT_ENABLED', true);
define('PROD_LOGIN_ATTEMPTS_LIMIT', 5);
define('PROD_LOGIN_LOCKOUT_DURATION', 900); // 15 minutes

// Production Session Settings
define('PROD_SESSION_LIFETIME', 7200); // 2 hours
define('PROD_SESSION_SECURE', true);   // Set to true if using HTTPS
define('PROD_SESSION_HTTPONLY', true);

// Production Timezone
define('PROD_TIMEZONE', 'Asia/Riyadh');

// Production Company Information
define('PROD_COMPANY_NAME', 'شركتك');
define('PROD_COMPANY_ADDRESS', 'عنوان شركتك');
define('PROD_COMPANY_PHONE', '+966 XX XXX XXXX');
define('PROD_COMPANY_EMAIL', '<EMAIL>');

// Production Notification Settings
define('PROD_NOTIFICATIONS_ENABLED', true);
define('PROD_EMAIL_NOTIFICATIONS', true);
define('PROD_SMS_NOTIFICATIONS', false); // Set to true if you have SMS service

// Production Maintenance Mode
define('PROD_MAINTENANCE_MODE', false);
define('PROD_MAINTENANCE_MESSAGE', 'النظام تحت الصيانة. يرجى المحاولة لاحقاً.');

// Production SSL Settings
define('PROD_FORCE_HTTPS', true);
define('PROD_SSL_VERIFY_PEER', true);

// Production API Settings (if needed)
define('PROD_API_ENABLED', false);
define('PROD_API_KEY', 'your-api-key-here');
define('PROD_API_RATE_LIMIT', 100); // requests per hour

// Production Monitoring
define('PROD_MONITORING_ENABLED', true);
define('PROD_HEALTH_CHECK_URL', '/health-check.php');

// Production CDN Settings (optional)
define('PROD_CDN_ENABLED', false);
define('PROD_CDN_URL', 'https://cdn.yourdomain.com');

// Production Database Connection Pool
define('PROD_DB_POOL_SIZE', 10);
define('PROD_DB_TIMEOUT', 30);

// Production Error Pages
define('PROD_ERROR_404_PAGE', '/errors/404.php');
define('PROD_ERROR_500_PAGE', '/errors/500.php');
define('PROD_ERROR_403_PAGE', '/errors/403.php');

// Production Backup Encryption
define('PROD_BACKUP_ENCRYPTION', true);
define('PROD_BACKUP_ENCRYPTION_KEY', 'your-backup-encryption-key');

// Production Two-Factor Authentication (optional)
define('PROD_2FA_ENABLED', false);
define('PROD_2FA_ISSUER', 'نظام إدارة الموظفين');

// Production Password Policy
define('PROD_PASSWORD_MIN_LENGTH', 8);
define('PROD_PASSWORD_REQUIRE_UPPERCASE', true);
define('PROD_PASSWORD_REQUIRE_LOWERCASE', true);
define('PROD_PASSWORD_REQUIRE_NUMBERS', true);
define('PROD_PASSWORD_REQUIRE_SYMBOLS', true);
define('PROD_PASSWORD_EXPIRY_DAYS', 90);

// Production Audit Trail
define('PROD_AUDIT_ENABLED', true);
define('PROD_AUDIT_RETENTION_DAYS', 365);

// Production Data Retention
define('PROD_DATA_RETENTION_ENABLED', true);
define('PROD_LOG_RETENTION_DAYS', 90);
define('PROD_SESSION_RETENTION_DAYS', 30);

// Production Compliance
define('PROD_GDPR_COMPLIANCE', true);
define('PROD_DATA_ANONYMIZATION', true);

// Production Integration Settings
define('PROD_INTEGRATION_ENABLED', false);
define('PROD_WEBHOOK_URL', '');
define('PROD_WEBHOOK_SECRET', '');

// Production Mobile App Settings (if applicable)
define('PROD_MOBILE_API_ENABLED', false);
define('PROD_MOBILE_APP_VERSION', '1.0.0');

// Production Analytics
define('PROD_ANALYTICS_ENABLED', false);
define('PROD_ANALYTICS_TRACKING_ID', '');

// Production Social Login (optional)
define('PROD_SOCIAL_LOGIN_ENABLED', false);
define('PROD_GOOGLE_CLIENT_ID', '');
define('PROD_GOOGLE_CLIENT_SECRET', '');

// Production File Storage
define('PROD_FILE_STORAGE_TYPE', 'local'); // local, s3, cloudinary
define('PROD_FILE_STORAGE_BUCKET', '');
define('PROD_FILE_STORAGE_REGION', '');

// Production Queue System (optional)
define('PROD_QUEUE_ENABLED', false);
define('PROD_QUEUE_DRIVER', 'database'); // database, redis, sqs

// Production Search Engine
define('PROD_SEARCH_ENABLED', true);
define('PROD_SEARCH_ENGINE', 'mysql'); // mysql, elasticsearch

// Production Localization
define('PROD_LOCALIZATION_ENABLED', true);
define('PROD_DEFAULT_LANGUAGE', 'ar');
define('PROD_SUPPORTED_LANGUAGES', ['ar', 'en']);

// Production Feature Flags
define('PROD_FEATURE_REPORTS', true);
define('PROD_FEATURE_EXPORTS', true);
define('PROD_FEATURE_IMPORTS', true);
define('PROD_FEATURE_NOTIFICATIONS', true);
define('PROD_FEATURE_CALENDAR', true);

// Production Version Information
define('PROD_APP_VERSION', '1.0.0');
define('PROD_APP_BUILD', '********');
define('PROD_APP_ENVIRONMENT', 'production');

?>

<!-- 
INSTRUCTIONS FOR HOSTINGER SETUP:

1. Copy this file to 'config-production.php'
2. Update all database settings with your actual Hostinger database details
3. Generate secure random keys for encryption and CSRF
4. Update email settings with your domain email
5. Set correct file paths for your hosting account
6. Enable HTTPS settings if you have SSL certificate
7. Test all settings before going live

SECURITY NOTES:
- Never commit config-production.php to version control
- Use strong, unique passwords and keys
- Enable all security features in production
- Regularly update and rotate security keys
- Monitor logs for suspicious activities

HOSTINGER SPECIFIC NOTES:
- Database host is usually 'localhost'
- Database names start with your username (u123456789_)
- File paths start with /home/<USER>/
- Email settings use smtp.hostinger.com
- Enable CloudFlare for additional security and performance
-->
