<?php
/**
 * Installation Script for Payroll System
 *
 * This script helps you install the payroll system on your hosting server
 */

// Prevent direct access if already installed
if (file_exists('includes/installed.lock')) {
    die('النظام مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف includes/installed.lock');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        // Database configuration step
        $db_host = $_POST['db_host'] ?? '';
        $db_user = $_POST['db_user'] ?? '';
        $db_pass = $_POST['db_pass'] ?? '';
        $db_name = $_POST['db_name'] ?? '';

        // Test database connection
        try {
            $test_conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
            if ($test_conn->connect_error) {
                throw new Exception("فشل الاتصال: " . $test_conn->connect_error);
            }

            // Update config file
            $config_content = file_get_contents('includes/config.php');
            $config_content = str_replace('u123456789_payroll', $db_name, $config_content);
            $config_content = str_replace('YourSecurePassword123!', $db_pass, $config_content);
            // Replace the second occurrence for database name
            $config_content = preg_replace('/u123456789_payroll/', $db_user, $config_content, 1);

            file_put_contents('includes/config.php', $config_content);

            $success = 'تم حفظ إعدادات قاعدة البيانات بنجاح!';
            $step = 3;

        } catch (Exception $e) {
            $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Database installation step
        try {
            require_once 'includes/config.php';

            // Read and execute SQL file
            $sql_content = file_get_contents('database.sql');
            $sql_statements = explode(';', $sql_content);

            foreach ($sql_statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    if (!$conn->query($statement)) {
                        throw new Exception("خطأ في تنفيذ الاستعلام: " . $conn->error);
                    }
                }
            }

            $success = 'تم إنشاء قاعدة البيانات بنجاح!';
            $step = 4;

        } catch (Exception $e) {
            $error = 'خطأ في إنشاء قاعدة البيانات: ' . $e->getMessage();
        }
    } elseif ($step == 4) {
        // Final step - create lock file
        file_put_contents('includes/installed.lock', date('Y-m-d H:i:s'));
        $success = 'تم تثبيت النظام بنجاح!';
        $step = 5;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة الموظفين والرواتب</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    تثبيت نظام إدارة الموظفين والرواتب
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    الخطوة <?php echo $step; ?> من 5
                </p>
            </div>

            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?php echo ($step / 5) * 100; ?>%"></div>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <div class="bg-white shadow-md rounded-lg p-6">
                <?php if ($step == 1): ?>
                    <h3 class="text-xl font-bold mb-4">مرحباً بك في معالج التثبيت</h3>
                    <p class="mb-4">سيساعدك هذا المعالج على تثبيت نظام إدارة الموظفين والرواتب على خادمك.</p>
                    <p class="mb-4">تأكد من أن لديك:</p>
                    <ul class="list-disc list-inside mb-6 space-y-2">
                        <li>قاعدة بيانات MySQL</li>
                        <li>اسم المستخدم وكلمة المرور لقاعدة البيانات</li>
                        <li>PHP 7.4 أو أحدث</li>
                    </ul>
                    <a href="?step=2" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 block text-center">
                        ابدأ التثبيت
                    </a>

                <?php elseif ($step == 2): ?>
                    <h3 class="text-xl font-bold mb-4">إعدادات قاعدة البيانات</h3>
                    <form method="POST">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">خادم قاعدة البيانات</label>
                            <input type="text" name="db_host" value="localhost" required
                                   class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">اسم قاعدة البيانات</label>
                            <input type="text" name="db_name" required
                                   class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500"
                                   placeholder="مثال: u123456789_payroll">
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">اسم المستخدم</label>
                            <input type="text" name="db_user" required
                                   class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500"
                                   placeholder="مثال: u123456789_payroll">
                        </div>
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2">كلمة المرور</label>
                            <input type="password" name="db_pass" required
                                   class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            اختبار الاتصال وحفظ الإعدادات
                        </button>
                    </form>

                <?php elseif ($step == 3): ?>
                    <h3 class="text-xl font-bold mb-4">إنشاء قاعدة البيانات</h3>
                    <p class="mb-4">سيتم الآن إنشاء الجداول المطلوبة في قاعدة البيانات.</p>
                    <form method="POST">
                        <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                            إنشاء قاعدة البيانات
                        </button>
                    </form>

                <?php elseif ($step == 4): ?>
                    <h3 class="text-xl font-bold mb-4">إنهاء التثبيت</h3>
                    <p class="mb-4">تم إنشاء قاعدة البيانات بنجاح. اضغط على "إنهاء التثبيت" لإكمال العملية.</p>
                    <form method="POST">
                        <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                            إنهاء التثبيت
                        </button>
                    </form>

                <?php elseif ($step == 5): ?>
                    <h3 class="text-xl font-bold mb-4 text-green-600">تم التثبيت بنجاح!</h3>
                    <p class="mb-4">تم تثبيت النظام بنجاح. يمكنك الآن تسجيل الدخول باستخدام:</p>
                    <div class="bg-gray-100 p-4 rounded mb-4">
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin</p>
                    </div>
                    <p class="mb-4 text-red-600"><strong>مهم:</strong> تأكد من تغيير كلمة المرور بعد تسجيل الدخول!</p>
                    <a href="index.php" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 block text-center">
                        الذهاب إلى النظام
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
