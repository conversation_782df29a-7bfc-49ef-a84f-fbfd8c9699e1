<?php
/**
 * Logout Page
 * 
 * This file handles user logout for the Employee Management System
 */

// Start session
session_start();

// Include functions file
require_once 'includes/functions.php';

// Check if user is logged in
if (is_logged_in()) {
    // Log activity
    log_activity('logout', 'تم تسجيل الخروج بنجاح', $_SESSION['user_id']);
    
    // Destroy session
    session_unset();
    session_destroy();
}

// Redirect to login page
redirect('login.php');
?>
