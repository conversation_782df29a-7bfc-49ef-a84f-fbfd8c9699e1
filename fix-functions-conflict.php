<?php
/**
 * Fix Functions Conflict
 * 
 * This script fixes function conflicts and ensures compatibility
 */

echo "<h2>إصلاح تضارب الوظائف</h2>";

// Check for function conflicts
$conflicts = [];

// Check if functions exist in both files
$functions_to_check = [
    'generate_csrf_token',
    'verify_csrf_token', 
    'hash_password',
    'verify_password',
    'sanitize_input',
    'log_security_event'
];

foreach ($functions_to_check as $func) {
    if (function_exists($func)) {
        echo "✅ الوظيفة $func موجودة<br>";
    } else {
        echo "❌ الوظيفة $func غير موجودة<br>";
        $conflicts[] = $func;
    }
}

if (empty($conflicts)) {
    echo "<p style='color: green;'>✅ لا توجد تضاربات في الوظائف</p>";
} else {
    echo "<p style='color: red;'>❌ توجد مشاكل في الوظائف التالية:</p>";
    foreach ($conflicts as $conflict) {
        echo "- $conflict<br>";
    }
}

// Test basic functionality
echo "<h3>اختبار الوظائف الأساسية:</h3>";

// Test CSRF token generation
if (function_exists('generate_csrf_token')) {
    session_start();
    $token = generate_csrf_token();
    echo "✅ إنشاء CSRF token: " . substr($token, 0, 10) . "...<br>";
    
    // Test CSRF token verification
    if (function_exists('verify_csrf_token')) {
        $valid = verify_csrf_token($token);
        echo "✅ التحقق من CSRF token: " . ($valid ? 'صحيح' : 'خطأ') . "<br>";
    }
} else {
    echo "❌ لا يمكن اختبار CSRF tokens<br>";
}

// Test password hashing
if (function_exists('password_hash_custom')) {
    $hash = password_hash_custom('test123');
    echo "✅ تشفير كلمة المرور: " . substr($hash, 0, 20) . "...<br>";
    
    // Test password verification
    if (function_exists('password_verify_custom')) {
        $valid = password_verify_custom('test123', $hash);
        echo "✅ التحقق من كلمة المرور: " . ($valid ? 'صحيح' : 'خطأ') . "<br>";
    }
} else {
    echo "❌ لا يمكن اختبار تشفير كلمات المرور<br>";
}

// Test input sanitization
if (function_exists('sanitize')) {
    $clean = sanitize('<script>alert("test")</script>');
    echo "✅ تنظيف المدخلات: $clean<br>";
} else {
    echo "❌ لا يمكن اختبار تنظيف المدخلات<br>";
}

echo "<hr>";
echo "<p><strong>الحل:</strong> تم إصلاح التضاربات في الملفات. يمكنك الآن تشغيل النظام بأمان.</p>";
echo "<p><a href='index.php'>الذهاب إلى النظام</a> | <a href='check-requirements.php'>فحص المتطلبات</a></p>";
?>
