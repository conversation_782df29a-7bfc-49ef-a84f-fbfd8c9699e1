<?php
/**
 * Installation Cleanup Script
 * 
 * This script removes installation files after successful deployment
 * Run this after confirming everything works correctly
 */

// Security check - only run if installation is complete
if (!file_exists('includes/installed.lock')) {
    die('❌ لا يمكن تشغيل هذا السكريبت قبل اكتمال التثبيت.');
}

// Files to be removed after installation
$installation_files = [
    'install.php',
    'check-requirements.php',
    'update_admin_password.php',
    'cleanup-installation.php', // This file itself
    'config-production.example.php',
    'includes/installed.lock.example',
    'includes/password_updated.lock'
];

// Optional files (ask user before removing)
$optional_files = [
    'health-check.php',
    'DEPLOYMENT-GUIDE.md',
    'README.md'
];

$action = $_GET['action'] ?? '';
$confirmed = $_GET['confirmed'] ?? '';

if ($action === 'cleanup' && $confirmed === 'yes') {
    $removed_files = [];
    $failed_files = [];
    
    // Remove installation files
    foreach ($installation_files as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                $removed_files[] = $file;
            } else {
                $failed_files[] = $file;
            }
        }
    }
    
    // Remove optional files if requested
    if (isset($_GET['remove_optional'])) {
        foreach ($optional_files as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $removed_files[] = $file;
                } else {
                    $failed_files[] = $file;
                }
            }
        }
    }
    
    // Show results
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نتائج التنظيف</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-gray-100 min-h-screen py-8">
        <div class="container mx-auto px-4 max-w-2xl">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                    <i class="fas fa-broom text-blue-600 ml-2"></i>
                    نتائج تنظيف ملفات التثبيت
                </h1>
                
                <?php if (!empty($removed_files)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        <h3 class="font-bold mb-2">✅ تم حذف الملفات التالية بنجاح:</h3>
                        <ul class="list-disc list-inside">
                            <?php foreach ($removed_files as $file): ?>
                                <li><?php echo $file; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($failed_files)): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <h3 class="font-bold mb-2">❌ فشل في حذف الملفات التالية:</h3>
                        <ul class="list-disc list-inside">
                            <?php foreach ($failed_files as $file): ?>
                                <li><?php echo $file; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <p class="mt-2 text-sm">يرجى حذف هذه الملفات يدوياً من File Manager.</p>
                    </div>
                <?php endif; ?>
                
                <div class="text-center mt-6">
                    <a href="index.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-home ml-2"></i>
                        الذهاب إلى النظام
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف ملفات التثبيت</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                <i class="fas fa-broom text-blue-600 ml-2"></i>
                تنظيف ملفات التثبيت
            </h1>
            
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-xl ml-2"></i>
                    <span class="font-bold">تحذير!</span>
                </div>
                <p class="mt-2">
                    هذا السكريبت سيحذف ملفات التثبيت نهائياً. تأكد من أن النظام يعمل بشكل صحيح قبل المتابعة.
                </p>
            </div>
            
            <div class="mb-6">
                <h3 class="font-bold text-lg mb-3">الملفات التي سيتم حذفها:</h3>
                
                <div class="bg-gray-50 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold mb-2 text-red-600">ملفات التثبيت (سيتم حذفها تلقائياً):</h4>
                    <ul class="list-disc list-inside text-sm space-y-1">
                        <?php foreach ($installation_files as $file): ?>
                            <?php if (file_exists($file)): ?>
                                <li class="text-gray-700"><?php echo $file; ?> ✓</li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold mb-2 text-yellow-600">ملفات اختيارية:</h4>
                    <ul class="list-disc list-inside text-sm space-y-1">
                        <?php foreach ($optional_files as $file): ?>
                            <?php if (file_exists($file)): ?>
                                <li class="text-gray-700"><?php echo $file; ?> (مفيد للمرجع)</li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                    <div class="mt-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="remove_optional" class="ml-2">
                            <span class="text-sm">حذف الملفات الاختيارية أيضاً</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6">
                <h3 class="font-bold mb-2">نصائح قبل التنظيف:</h3>
                <ul class="list-disc list-inside text-sm space-y-1">
                    <li>تأكد من أن النظام يعمل بشكل صحيح</li>
                    <li>اختبر تسجيل الدخول وجميع الوظائف</li>
                    <li>احفظ نسخة احتياطية من قاعدة البيانات</li>
                    <li>احتفظ بنسخة من ملفات التثبيت في مكان آمن</li>
                </ul>
            </div>
            
            <div class="text-center">
                <button onclick="confirmCleanup()" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                    <i class="fas fa-trash ml-2"></i>
                    بدء التنظيف
                </button>
                
                <a href="health-check.php" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                    <i class="fas fa-heartbeat ml-2"></i>
                    فحص صحة النظام
                </a>
                
                <a href="index.php" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                    <i class="fas fa-home ml-2"></i>
                    العودة للنظام
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function confirmCleanup() {
            const removeOptional = document.getElementById('remove_optional').checked;
            
            const message = 'هل أنت متأكد من أنك تريد حذف ملفات التثبيت؟\n\n' +
                           'هذا الإجراء لا يمكن التراجع عنه!\n\n' +
                           'تأكد من أن النظام يعمل بشكل صحيح قبل المتابعة.';
            
            if (confirm(message)) {
                let url = '?action=cleanup&confirmed=yes';
                if (removeOptional) {
                    url += '&remove_optional=1';
                }
                window.location.href = url;
            }
        }
    </script>
</body>
</html>
