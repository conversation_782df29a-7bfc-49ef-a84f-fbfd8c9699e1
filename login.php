<?php
/**
 * Login Page
 *
 * This file handles user authentication for the Employee Management System
 */

// Start session
session_start();

// Include configuration, functions, and security
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// Initialize variables
$username = '';
$error = '';

// Process login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Rate limiting check
    $client_ip = $_SERVER['REMOTE_ADDR'];
    if (!check_rate_limit($client_ip, 5, 900)) { // 5 attempts per 15 minutes
        $error = 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة. يرجى المحاولة بعد 15 دقيقة.';
    } elseif (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $username = sanitize_input($_POST['username']);
        $password = $_POST['password']; // Don't sanitize password before verification

        // Validate input
        if (empty($username) || empty($password)) {
            $error = 'يرجى إدخال اسم المستخدم وكلمة المرور.';
        } else {
            // Check credentials
            $sql = "SELECT id, username, password, name, role FROM users WHERE username = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();

                // Verify password
                if (verify_password($password, $user['password'])) {
                    // Set session variables
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['role'] = $user['role'];

                    // Update last login time
                    $update_sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param("i", $user['id']);
                    $update_stmt->execute();

                    // Log security event
                    log_security_event('login', 'تم تسجيل الدخول بنجاح', $user['id']);

                    // Redirect to dashboard
                    header('Location: index.php');
                    exit();
                } else {
                    $error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
                }
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | <?php echo SYSTEM_NAME; ?></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#FF6B00', // Orange
                            dark: '#E05A00',
                            light: '#FF8C3F'
                        },
                        secondary: {
                            DEFAULT: '#1F2937', // Dark Gray
                            dark: '#111827',
                            light: '#374151'
                        }
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo (Arabic) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2"><?php echo SYSTEM_NAME; ?></h1>
            <p class="text-gray-600">يرجى تسجيل الدخول للوصول إلى لوحة التحكم</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><?php echo $error; ?></span>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

            <div class="mb-4">
                <label for="username" class="block text-gray-700 text-sm font-bold mb-2">اسم المستخدم</label>
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-user text-gray-400"></i>
                    </div>
                    <input type="text" id="username" name="username" value="<?php echo $username; ?>" class="block w-full pr-10 border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" placeholder="أدخل اسم المستخدم" required>
                </div>
            </div>

            <div class="mb-6">
                <label for="password" class="block text-gray-700 text-sm font-bold mb-2">كلمة المرور</label>
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <input type="password" id="password" name="password" class="block w-full pr-10 border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" placeholder="أدخل كلمة المرور" required>
                </div>
            </div>

            <div class="mb-6">
                <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition duration-300">
                    تسجيل الدخول <i class="fas fa-sign-in-alt mr-2"></i>
                </button>
            </div>
        </form>

        <div class="text-center text-sm text-gray-500">
            <p>البيانات الافتراضية: admin / admin</p>
        </div>
    </div>
</body>
</html>
