<?php
/**
 * Employees List Page
 * 
 * This file displays a list of all employees
 */

// Include header
require_once '../includes/header.php';

// Initialize variables
$search = '';
$department = '';
$status = '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Process search and filter
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['search'])) {
    $search = sanitize($_GET['search']);
    $department = isset($_GET['department']) ? sanitize($_GET['department']) : '';
    $status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
}

// Build query
$sql = "SELECT * FROM employees WHERE 1=1";
$count_sql = "SELECT COUNT(*) as total FROM employees WHERE 1=1";
$params = [];
$types = "";

if (!empty($search)) {
    $sql .= " AND (name LIKE ? OR employee_number LIKE ? OR position LIKE ? OR phone LIKE ? OR email LIKE ?)";
    $count_sql .= " AND (name LIKE ? OR employee_number LIKE ? OR position LIKE ? OR phone LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    $types .= "sssss";
}

if (!empty($department)) {
    $sql .= " AND department = ?";
    $count_sql .= " AND department = ?";
    $params[] = $department;
    $types .= "s";
}

if (!empty($status)) {
    $sql .= " AND status = ?";
    $count_sql .= " AND status = ?";
    $params[] = $status;
    $types .= "s";
}

// Add order by and limit
$sql .= " ORDER BY name ASC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$types .= "ii";

// Get total records
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_types = substr($types, 0, -2); // Remove the 'ii' for limit and offset
    $count_params = array_slice($params, 0, -2); // Remove limit and offset params
    
    if (!empty($count_params)) {
        $count_stmt->bind_param($count_types, ...$count_params);
    }
}
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$count_row = $count_result->fetch_assoc();
$total_records = $count_row['total'];
$total_pages = ceil($total_records / $limit);

// Get employees
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$employees = [];
while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
}

// Get departments for filter
$dept_sql = "SELECT DISTINCT department FROM employees ORDER BY department";
$dept_result = $conn->query($dept_sql);
$departments = [];
if ($dept_result) {
    while ($row = $dept_result->fetch_assoc()) {
        $departments[] = $row['department'];
    }
}
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">إدارة الموظفين</h1>
    <a href="add.php" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
        <i class="fas fa-user-plus ml-2"></i> إضافة موظف جديد
    </a>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
            <input type="text" id="search" name="search" value="<?php echo $search; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" placeholder="اسم، رقم، وظيفة...">
        </div>
        
        <div>
            <label for="department" class="block text-sm font-medium text-gray-700 mb-1">القسم</label>
            <select id="department" name="department" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <?php foreach ($departments as $dept): ?>
                    <option value="<?php echo $dept; ?>" <?php echo $department === $dept ? 'selected' : ''; ?>>
                        <?php echo $dept; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
            <select id="status" name="status" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-search ml-2"></i> بحث
            </button>
            <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mr-2 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                <i class="fas fa-redo ml-2"></i> إعادة تعيين
            </a>
        </div>
    </form>
</div>

<!-- Employees List -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b">
        <h2 class="text-xl font-semibold">قائمة الموظفين</h2>
        <p class="text-gray-500 text-sm">إجمالي الموظفين: <?php echo $total_records; ?></p>
    </div>
    
    <?php if (empty($employees)): ?>
        <div class="p-6 text-center">
            <p class="text-gray-500">لا يوجد موظفين للعرض</p>
        </div>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الموظف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الاسم
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المسمى الوظيفي
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            القسم
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ التعيين
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الراتب الأساسي
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($employees as $employee): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $employee['employee_number']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <?php if (!empty($employee['image'])): ?>
                                        <img class="h-10 w-10 rounded-full ml-3" src="../assets/uploads/employees/<?php echo $employee['image']; ?>" alt="<?php echo $employee['name']; ?>">
                                    <?php else: ?>
                                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center ml-3">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo $employee['name']; ?>
                                        </div>
                                        <?php if (!empty($employee['email'])): ?>
                                            <div class="text-sm text-gray-500">
                                                <?php echo $employee['email']; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $employee['position']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $employee['department']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo format_date($employee['join_date']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo format_currency($employee['basic_salary']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($employee['status'] === 'active'): ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        نشط
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        غير نشط
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="view.php?id=<?php echo $employee['id']; ?>" class="text-blue-600 hover:text-blue-900 ml-3">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="edit.php?id=<?php echo $employee['id']; ?>" class="text-yellow-600 hover:text-yellow-900 ml-3">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="delete.php?id=<?php echo $employee['id']; ?>" class="text-red-600 hover:text-red-900 ml-3" onclick="return confirm('هل أنت متأكد من حذف هذا الموظف؟');">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        عرض <?php echo count($employees); ?> من <?php echo $total_records; ?> موظف
                    </div>
                    <div class="flex space-x-1 space-x-reverse">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department); ?>&status=<?php echo urlencode($status); ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                السابق
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department); ?>&status=<?php echo urlencode($status); ?>" class="px-3 py-1 <?php echo $i === $page ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-gray-50'; ?> border border-gray-300 rounded-md text-sm font-medium">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department); ?>&status=<?php echo urlencode($status); ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                التالي
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
