<?php
/**
 * Demo Dashboard - Simple SQLite Demo Dashboard
 */

// Include the simple configuration
require_once 'includes/config-simple.php';
require_once 'includes/functions.php';

// Check if logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: demo.php');
    exit();
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: demo.php');
    exit();
}

// Sample data for demo
$stats = [
    'total_employees' => 5,
    'active_employees' => 4,
    'present_today' => 3,
    'absent_today' => 1
];

$recent_employees = [
    ['id' => 1, 'employee_number' => 'EMP001', 'name' => 'أحمد محمد', 'position' => 'مطور برمجيات', 'join_date' => '2024-01-15'],
    ['id' => 2, 'employee_number' => 'EMP002', 'name' => 'فاطمة أحمد', 'position' => 'محاسبة', 'join_date' => '2024-02-01'],
    ['id' => 3, 'employee_number' => 'EMP003', 'name' => 'محمد علي', 'position' => 'مدير مشروع', 'join_date' => '2024-02-15']
];

$activities = [
    ['action' => 'login', 'description' => 'تم تسجيل الدخول بنجاح', 'name' => 'مدير النظام', 'timestamp' => date('Y-m-d H:i:s')],
    ['action' => 'add_employee', 'description' => 'تم إضافة موظف جديد: أحمد محمد', 'name' => 'مدير النظام', 'timestamp' => date('Y-m-d H:i:s', strtotime('-1 hour'))],
    ['action' => 'attendance', 'description' => 'تم تسجيل حضور الموظف: فاطمة أحمد', 'name' => 'مدير النظام', 'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours'))]
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - عرض توضيحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-users text-2xl ml-3"></i>
                    <h1 class="text-xl font-bold">نظام إدارة الموظفين - عرض توضيحي</h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-sm">مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                    <a href="?logout=1" class="bg-red-500 hover:bg-red-600 px-3 py-1 rounded text-sm transition duration-300">
                        <i class="fas fa-sign-out-alt ml-1"></i> خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Alert -->
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-info-circle ml-2"></i>
                <span>هذا عرض توضيحي يعمل بـ SQLite. جميع البيانات تجريبية.</span>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-blue-500">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">إجمالي الموظفين</p>
                        <h2 class="text-3xl font-bold text-blue-600"><?php echo $stats['total_employees']; ?></h2>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-users text-blue-500 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-green-500">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الموظفين النشطين</p>
                        <h2 class="text-3xl font-bold text-green-600"><?php echo $stats['active_employees']; ?></h2>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-user-check text-green-500 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-orange-500">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الحاضرين اليوم</p>
                        <h2 class="text-3xl font-bold text-orange-600"><?php echo $stats['present_today']; ?></h2>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-calendar-check text-orange-500 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-red-500">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">الغائبين اليوم</p>
                        <h2 class="text-3xl font-bold text-red-600"><?php echo $stats['absent_today']; ?></h2>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-calendar-times text-red-500 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <button onclick="showDemo('employee')" class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-4 text-center transition duration-300">
                <i class="fas fa-user-plus text-2xl mb-2"></i>
                <p>إضافة موظف جديد</p>
            </button>
            <button onclick="showDemo('attendance')" class="bg-green-600 hover:bg-green-700 text-white rounded-lg p-4 text-center transition duration-300">
                <i class="fas fa-clipboard-check text-2xl mb-2"></i>
                <p>تسجيل الحضور</p>
            </button>
            <button onclick="showDemo('salary')" class="bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-4 text-center transition duration-300">
                <i class="fas fa-calculator text-2xl mb-2"></i>
                <p>حساب المرتبات</p>
            </button>
            <button onclick="showDemo('report')" class="bg-orange-600 hover:bg-orange-700 text-white rounded-lg p-4 text-center transition duration-300">
                <i class="fas fa-chart-bar text-2xl mb-2"></i>
                <p>التقارير</p>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Recent Activities -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-history ml-2 text-blue-600"></i> آخر النشاطات
                </h3>
                <ul class="divide-y">
                    <?php foreach ($activities as $activity): ?>
                        <li class="py-3">
                            <div class="flex items-start">
                                <div class="bg-gray-100 p-2 rounded-full ml-3">
                                    <i class="fas fa-user-circle text-gray-500"></i>
                                </div>
                                <div>
                                    <p class="font-medium"><?php echo $activity['name']; ?></p>
                                    <p class="text-sm text-gray-600"><?php echo $activity['description']; ?></p>
                                    <p class="text-xs text-gray-500 mt-1"><?php echo date('Y-m-d H:i', strtotime($activity['timestamp'])); ?></p>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <!-- Recent Employees -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-user-plus ml-2 text-blue-600"></i> الموظفين الجدد
                </h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الرقم</th>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المنصب</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_employees as $employee): ?>
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900"><?php echo $employee['employee_number']; ?></td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-blue-600">
                                        <?php echo $employee['name']; ?>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><?php echo $employee['position']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Demo Modal -->
        <div id="demoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 id="modalTitle" class="text-lg font-bold"></h3>
                        <button onclick="closeDemo()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="modalContent" class="text-gray-600"></div>
                    <div class="mt-6 text-center">
                        <button onclick="closeDemo()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center">
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p class="text-yellow-800">
                    <i class="fas fa-lightbulb ml-1"></i>
                    <strong>هذا عرض توضيحي:</strong> لتجربة النظام الكامل، استخدم 
                    <a href="index.php" class="text-blue-600 hover:underline">النظام الأصلي</a>
                    أو ارفعه على 
                    <a href="hostinger-setup.md" class="text-blue-600 hover:underline">استضافة Hostinger</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function showDemo(type) {
            const modal = document.getElementById('demoModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            
            const demos = {
                employee: {
                    title: 'إضافة موظف جديد',
                    content: 'في النظام الكامل، يمكنك إضافة موظفين جدد مع جميع البيانات الشخصية والوظيفية، رفع الصور، وإدارة المعلومات بسهولة.'
                },
                attendance: {
                    title: 'تسجيل الحضور والانصراف',
                    content: 'يمكن تسجيل حضور وانصراف الموظفين يومياً، تتبع التأخير، حساب ساعات العمل الإضافية، وإدارة الإجازات.'
                },
                salary: {
                    title: 'حساب المرتبات',
                    content: 'النظام يحسب المرتبات تلقائياً بناءً على الراتب الأساسي، البدلات، الخصومات، والعمل الإضافي مع إمكانية طباعة كشوف المرتبات.'
                },
                report: {
                    title: 'التقارير والإحصائيات',
                    content: 'تقارير شاملة للحضور والغياب، المرتبات، أداء الموظفين، وإحصائيات مفصلة مع إمكانية التصدير والطباعة.'
                }
            };
            
            title.textContent = demos[type].title;
            content.textContent = demos[type].content;
            modal.classList.remove('hidden');
        }
        
        function closeDemo() {
            document.getElementById('demoModal').classList.add('hidden');
        }
    </script>
</body>
</html>
