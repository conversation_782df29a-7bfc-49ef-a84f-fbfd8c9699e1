# Apache Configuration for Payroll System
# This file provides security and performance optimizations

# Enable rewrite engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Protect sensitive files and directories
<FilesMatch "\.(sql|log|txt|md)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect includes directory
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# Protect backup directory
<Directory "backup">
    Order Allow,Deny
    Deny from all
</Directory>

# Protect config files
<Files "config*.php">
    Order Allow,Deny
    Deny from all
    <RequireAll>
        Require local
    </RequireAll>
</Files>

# Hide .htaccess file
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Error pages (optional)
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

# Prevent access to PHP error logs
<Files "error_log">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to version control files
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# PHP settings for better security
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log error_log
    php_flag expose_php Off
</IfModule>

# Redirect to HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Pretty URLs (optional - for future use)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^([^/]+)/?$ index.php?page=$1 [L,QSA]
