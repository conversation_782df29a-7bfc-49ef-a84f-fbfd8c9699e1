<?php
/**
 * Common Functions File
 * 
 * This file contains common functions used throughout the Employee Management System
 */

// Include configuration file
require_once 'config.php';

/**
 * Sanitize input data to prevent XSS attacks
 * 
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate and sanitize input
 * 
 * @param string $input Input data
 * @param string $type Type of validation (text, email, number, date, etc.)
 * @return mixed Sanitized data or false if validation fails
 */
function validate_input($input, $type = 'text') {
    $input = sanitize($input);
    
    switch ($type) {
        case 'email':
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                return false;
            }
            break;
        case 'number':
            if (!is_numeric($input)) {
                return false;
            }
            break;
        case 'date':
            $date = DateTime::createFromFormat('Y-m-d', $input);
            if (!$date || $date->format('Y-m-d') !== $input) {
                return false;
            }
            break;
        case 'time':
            $time = DateTime::createFromFormat('H:i:s', $input);
            if (!$time || $time->format('H:i:s') !== $input) {
                return false;
            }
            break;
    }
    
    return $input;
}

/**
 * Generate a secure password hash
 * 
 * @param string $password Plain text password
 * @return string Hashed password
 */
function password_hash_custom($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
}

/**
 * Verify password against hash
 * 
 * @param string $password Plain text password
 * @param string $hash Hashed password
 * @return bool True if password matches hash
 */
function password_verify_custom($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate a CSRF token
 * 
 * @return string CSRF token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * 
 * @param string $token Token to verify
 * @return bool True if token is valid
 */
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        return false;
    }
    return true;
}

/**
 * Redirect to a URL
 * 
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * Check if user is logged in
 * 
 * @return bool True if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Format date according to system settings
 * 
 * @param string $date Date to format
 * @param string $format Format to use (default: system date format)
 * @return string Formatted date
 */
function format_date($date, $format = null) {
    if (empty($date)) {
        return '';
    }
    
    if ($format === null) {
        $format = DATE_FORMAT;
    }
    
    $date_obj = new DateTime($date);
    return $date_obj->format($format);
}

/**
 * Format time according to system settings
 * 
 * @param string $time Time to format
 * @param string $format Format to use (default: system time format)
 * @return string Formatted time
 */
function format_time($time, $format = null) {
    if (empty($time)) {
        return '';
    }
    
    if ($format === null) {
        $format = TIME_FORMAT;
    }
    
    $time_obj = new DateTime($time);
    return $time_obj->format($format);
}

/**
 * Format currency according to system settings
 * 
 * @param float $amount Amount to format
 * @return string Formatted amount
 */
function format_currency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY_SYMBOL;
}

/**
 * Get current date and time
 * 
 * @param string $format Format to use (default: 'Y-m-d H:i:s')
 * @return string Current date and time
 */
function current_datetime($format = 'Y-m-d H:i:s') {
    $date = new DateTime();
    return $date->format($format);
}

/**
 * Log system activity
 * 
 * @param string $action Action performed
 * @param string $description Description of the action
 * @param int $user_id ID of the user who performed the action
 * @return bool True if log was created successfully
 */
function log_activity($action, $description, $user_id = null) {
    global $conn;
    
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    $action = sanitize($action);
    $description = sanitize($description);
    $user_id = (int)$user_id;
    $timestamp = current_datetime();
    
    $sql = "INSERT INTO activity_log (user_id, action, description, timestamp) 
            VALUES (?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isss", $user_id, $action, $description, $timestamp);
    
    return $stmt->execute();
}

/**
 * Display alert message
 * 
 * @param string $message Message to display
 * @param string $type Type of alert (success, danger, warning, info)
 * @return string HTML for alert message
 */
function display_alert($message, $type = 'info') {
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

/**
 * Set flash message
 * 
 * @param string $message Message to display
 * @param string $type Type of alert (success, danger, warning, info)
 * @return void
 */
function set_flash_message($message, $type = 'info') {
    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Display flash message
 * 
 * @return string HTML for flash message
 */
function display_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $flash = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return display_alert($flash['message'], $flash['type']);
    }
    return '';
}
?>
