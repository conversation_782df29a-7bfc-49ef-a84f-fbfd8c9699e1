# دليل التشغيل المحلي - نظام إدارة الموظفين والرواتب

## 🚀 طرق تشغيل التطبيق محلياً

### الطريقة 1: استخدام XAMPP (الأسهل)

#### تحميل وتثبيت XAMPP:
1. اذهب إلى: https://www.apachefriends.org/download.html
2. حمّل XAMPP للويندوز
3. ثبّت XAMPP في `C:\xampp`

#### إعداد المشروع:
1. انسخ مجلد المشروع إلى: `C:\xampp\htdocs\payroll`
2. شغّل XAMPP Control Panel
3. ابدأ تشغيل Apache و MySQL
4. اذهب إلى: http://localhost/payroll

#### إنشاء قاعدة البيانات:
1. اذهب إلى: http://localhost/phpmyadmin
2. أنشئ قاعدة بيانات جديدة باسم: `payroll_system`
3. استورد ملف `database.sql`

---

### الطريقة 2: استخدام WAMP (بديل لـ XAMPP)

#### تحميل وتثبيت WAMP:
1. اذهب إلى: https://www.wampserver.com/en/
2. حمّل وثبّت WAMP
3. انسخ المشروع إلى: `C:\wamp64\www\payroll`
4. اذهب إلى: http://localhost/payroll

---

### الطريقة 3: استخدام خادم PHP المدمج (للتطوير السريع)

#### المتطلبات:
- PHP مثبت ومتاح في PATH
- SQLite أو MySQL منفصل

#### الخطوات:
```bash
# انتقل إلى مجلد المشروع
cd "d:\hostinger site\newpayroll"

# شغّل خادم PHP المدمج
php -S localhost:8000
```

#### ملاحظة:
هذه الطريقة تحتاج إعداد قاعدة بيانات منفصلة

---

## 🛠️ إعداد قاعدة البيانات المحلية

### مع XAMPP/WAMP:
1. افتح phpMyAdmin: http://localhost/phpmyadmin
2. أنشئ قاعدة بيانات: `payroll_system`
3. اختر Character set: `utf8mb4_unicode_ci`
4. استورد ملف `database.sql`

### إعدادات الاتصال المحلية:
في ملف `includes/config.php`:
```php
// Development settings (already configured)
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'payroll_system');
```

---

## 🧪 اختبار التطبيق محلياً

### 1. فحص المتطلبات:
- اذهب إلى: http://localhost/payroll/check-requirements.php
- تأكد من أن جميع المتطلبات متوفرة

### 2. تشغيل معالج التثبيت:
- اذهب إلى: http://localhost/payroll/install.php
- اتبع خطوات التثبيت
- استخدم إعدادات قاعدة البيانات المحلية

### 3. تسجيل الدخول:
- اذهب إلى: http://localhost/payroll
- اسم المستخدم: `admin`
- كلمة المرور: `admin`

### 4. اختبار الوظائف:
- ✅ إضافة موظف جديد
- ✅ تسجيل حضور
- ✅ حساب راتب
- ✅ عرض التقارير
- ✅ رفع صورة موظف

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "Database connection failed"
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات قاعدة البيانات في config.php
3. تأكد من وجود قاعدة البيانات `payroll_system`

### مشكلة: "Permission denied" لرفع الملفات
**الحل:**
1. تأكد من صلاحيات مجلد `assets/uploads`
2. في Windows، انقر بالزر الأيمن > Properties > Security
3. أعط صلاحيات كاملة للمجلد

### مشكلة: الخطوط العربية لا تظهر بشكل صحيح
**الحل:**
1. تأكد من أن قاعدة البيانات تستخدم `utf8mb4_unicode_ci`
2. تحقق من إعدادات PHP للـ charset
3. تأكد من حفظ الملفات بترميز UTF-8

### مشكلة: صفحة بيضاء أو أخطاء PHP
**الحل:**
1. فعّل عرض الأخطاء في PHP:
   ```php
   ini_set('display_errors', 1);
   error_reporting(E_ALL);
   ```
2. تحقق من ملف error.log
3. تأكد من إصدار PHP (7.4+)

---

## 📱 اختبار الاستجابة (Responsive)

### اختبر على أحجام شاشات مختلفة:
1. **Desktop:** 1920x1080
2. **Tablet:** 768x1024
3. **Mobile:** 375x667

### أدوات الاختبار:
- Chrome DevTools (F12)
- Firefox Responsive Design Mode
- Edge DevTools

---

## 🚀 نصائح للتطوير المحلي

### 1. استخدام Live Reload:
```bash
# إذا كان لديك Node.js
npm install -g live-server
live-server --port=8080
```

### 2. تفعيل Debug Mode:
في `includes/config.php`:
```php
// للتطوير المحلي فقط
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

### 3. نسخ احتياطية سريعة:
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root payroll_system > backup_local.sql

# نسخ احتياطي للملفات
xcopy "d:\hostinger site\newpayroll" "d:\backup\payroll" /E /I
```

### 4. اختبار الأداء:
- استخدم Chrome DevTools > Network
- راقب أوقات تحميل الصفحات
- تحقق من استهلاك الذاكرة

---

## 📋 قائمة اختبار شاملة

### ✅ الوظائف الأساسية:
- [ ] تسجيل الدخول والخروج
- [ ] إضافة/تعديل/حذف موظف
- [ ] رفع صورة موظف
- [ ] تسجيل حضور وانصراف
- [ ] حساب المرتبات
- [ ] عرض التقارير
- [ ] طباعة كشف راتب

### ✅ الأمان:
- [ ] حماية من SQL Injection
- [ ] حماية CSRF
- [ ] تشفير كلمات المرور
- [ ] حماية الملفات الحساسة
- [ ] تسجيل الأنشطة

### ✅ واجهة المستخدم:
- [ ] التصميم المتجاوب
- [ ] دعم اللغة العربية
- [ ] سهولة الاستخدام
- [ ] رسائل الخطأ والنجاح
- [ ] التنقل السلس

### ✅ الأداء:
- [ ] سرعة تحميل الصفحات
- [ ] استجابة قاعدة البيانات
- [ ] تحسين الصور
- [ ] ضغط الملفات

---

## 🎯 الخطوة التالية

بعد اختبار التطبيق محلياً بنجاح:

1. **اختبر جميع الوظائف** - تأكد من عمل كل شيء
2. **اجمع الملاحظات** - سجل أي مشاكل أو تحسينات
3. **أصلح المشاكل** - إن وجدت
4. **احفظ نسخة احتياطية** - من الإصدار النهائي
5. **ارفع على Hostinger** - باستخدام الأدلة المرفقة

---

**نصيحة:** اختبر التطبيق محلياً بدقة قبل الرفع لتجنب المشاكل في الإنتاج!
