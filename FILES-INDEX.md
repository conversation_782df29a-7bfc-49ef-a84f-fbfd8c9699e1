# فهرس ملفات نظام إدارة الموظفين والرواتب

## 📁 الملفات الأساسية

### ملفات التطبيق الرئيسية
- `index.php` - الصفحة الرئيسية ولوحة التحكم
- `login.php` - صفحة تسجيل الدخول
- `logout.php` - تسجيل الخروج
- `fix_login.php` - إصلاح مشاكل تسجيل الدخول

### ملفات قاعدة البيانات
- `database.sql` - هيكل قاعدة البيانات والبيانات الأولية
- `includes/config.php` - إعدادات الاتصال بقاعدة البيانات

### ملفات التثبيت والإعداد
- `install.php` - معالج التثبيت التفاعلي
- `check-requirements.php` - فحص متطلبات النظام
- `health-check.php` - فحص صحة النظام بعد التثبيت
- `cleanup-installation.php` - تنظيف ملفات التثبيت
- `update_admin_password.php` - تحديث كلمة مرور المدير

### ملفات الأمان والحماية
- `.htaccess` - إعدادات Apache للأمان والحماية
- `includes/security.php` - وظائف الأمان والحماية
- `includes/functions.php` - الوظائف العامة للنظام

### ملفات التوثيق
- `README.md` - دليل التثبيت والاستخدام الشامل
- `DEPLOYMENT-GUIDE.md` - دليل النشر السريع
- `hostinger-setup.md` - دليل الإعداد المفصل لـ Hostinger
- `FILES-INDEX.md` - هذا الملف (فهرس الملفات)

### ملفات التكوين
- `config-production.example.php` - مثال على إعدادات الإنتاج
- `includes/installed.lock.example` - مثال على ملف قفل التثبيت

---

## 📂 مجلدات النظام

### `/includes/` - الملفات المساعدة
- `config.php` - إعدادات قاعدة البيانات والنظام
- `functions.php` - الوظائف العامة
- `security.php` - وظائف الأمان
- `header.php` - رأس الصفحة والقائمة
- `footer.php` - تذييل الصفحة

### `/employees/` - إدارة الموظفين
- `index.php` - قائمة الموظفين
- `add.php` - إضافة موظف جديد
- `edit.php` - تعديل بيانات موظف
- `view.php` - عرض تفاصيل موظف
- `delete.php` - حذف موظف

### `/attendance/` - إدارة الحضور
- `index.php` - قائمة سجلات الحضور
- `record.php` - تسجيل حضور جديد
- `edit.php` - تعديل سجل حضور
- `delete.php` - حذف سجل حضور

### `/salaries/` - إدارة المرتبات
- `index.php` - قائمة المرتبات
- `calculate.php` - حساب المرتبات
- `view.php` - عرض تفاصيل راتب
- `print.php` - طباعة كشف راتب

### `/reports/` - التقارير
- `index.php` - قائمة التقارير
- `attendance.php` - تقرير الحضور
- `salaries.php` - تقرير المرتبات
- `employees.php` - تقرير الموظفين

### `/settings/` - الإعدادات
- `index.php` - الإعدادات العامة
- `profile.php` - الملف الشخصي
- `users.php` - إدارة المستخدمين
- `backup.php` - النسخ الاحتياطية

### `/assets/` - الملفات الثابتة
- `/css/` - ملفات التنسيق
- `/js/` - ملفات JavaScript
- `/images/` - الصور والأيقونات
- `/fonts/` - الخطوط
- `/uploads/` - الملفات المرفوعة
  - `/employees/` - صور الموظفين

### `/backup/` - النسخ الاحتياطية
- ملفات النسخ الاحتياطية لقاعدة البيانات

---

## 🔧 ملفات التطوير والصيانة

### ملفات مؤقتة (يتم حذفها بعد التثبيت)
- `install.php` ❌
- `check-requirements.php` ❌
- `update_admin_password.php` ❌
- `cleanup-installation.php` ❌
- `config-production.example.php` ❌

### ملفات اختيارية (يمكن الاحتفاظ بها)
- `health-check.php` ✅ (مفيد للمراقبة)
- `README.md` ✅ (للمرجع)
- `DEPLOYMENT-GUIDE.md` ✅ (للمرجع)
- `FILES-INDEX.md` ✅ (هذا الملف)

---

## 📋 قائمة مراجعة الملفات

### ✅ ملفات مطلوبة للعمل
- [ ] `index.php`
- [ ] `login.php`
- [ ] `logout.php`
- [ ] `includes/config.php`
- [ ] `includes/functions.php`
- [ ] `includes/security.php`
- [ ] `includes/header.php`
- [ ] `includes/footer.php`
- [ ] `.htaccess`
- [ ] `database.sql`

### ✅ مجلدات مطلوبة
- [ ] `/employees/`
- [ ] `/attendance/`
- [ ] `/salaries/`
- [ ] `/reports/`
- [ ] `/settings/`
- [ ] `/assets/`
- [ ] `/backup/`
- [ ] `/includes/`

### ✅ صلاحيات الملفات
- [ ] مجلد `/assets/uploads/` قابل للكتابة (755)
- [ ] مجلد `/backup/` قابل للكتابة (755)
- [ ] ملف `.htaccess` موجود ويعمل
- [ ] ملفات PHP قابلة للتنفيذ (644)

---

## 🚀 خطوات ما بعد التثبيت

### 1. التحقق من الملفات
```bash
# تحقق من وجود الملفات الأساسية
ls -la index.php login.php includes/config.php

# تحقق من صلاحيات المجلدات
ls -ld assets/uploads/ backup/
```

### 2. تنظيف ملفات التثبيت
- احذف ملفات التثبيت المؤقتة
- احتفظ بملفات التوثيق للمرجع
- تأكد من عمل النظام قبل الحذف

### 3. النسخ الاحتياطية
- احفظ نسخة من جميع الملفات
- احفظ نسخة من قاعدة البيانات
- اختبر استعادة النسخ الاحتياطية

### 4. المراقبة
- راقب ملفات السجلات
- تحقق من أداء النظام
- راجع تقارير الأمان

---

## 📞 الدعم والمساعدة

### إذا كان ملف مفقود:
1. تحقق من النسخة الاحتياطية
2. راجع قائمة الملفات أعلاه
3. أعد تحميل الملف من المصدر

### إذا كان هناك خطأ في الصلاحيات:
1. تحقق من صلاحيات المجلدات (755)
2. تحقق من صلاحيات الملفات (644)
3. تأكد من أن مجلدات الرفع قابلة للكتابة

### للحصول على المساعدة:
- راجع `README.md` للتوثيق الشامل
- استخدم `health-check.php` لفحص النظام
- راجع `DEPLOYMENT-GUIDE.md` لحل المشاكل

---

**ملاحظة:** احتفظ بهذا الملف كمرجع لهيكل النظام وتنظيم الملفات.
