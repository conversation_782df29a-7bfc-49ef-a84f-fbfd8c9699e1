<?php
/**
 * Fix MySQL Connection for Local Development
 */

echo "<h2>إصلاح اتصال MySQL للتطوير المحلي</h2>";

// Test different MySQL configurations
$configs = [
    ['host' => 'localhost', 'user' => 'root', 'pass' => '', 'name' => 'payroll_system'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'root', 'name' => 'payroll_system'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'mysql', 'name' => 'payroll_system'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => '', 'name' => 'payroll_system'],
];

echo "<h3>اختبار إعدادات MySQL المختلفة:</h3>";

$working_config = null;

foreach ($configs as $i => $config) {
    echo "<p><strong>التكوين " . ($i + 1) . ":</strong><br>";
    echo "Host: {$config['host']}<br>";
    echo "User: {$config['user']}<br>";
    echo "Password: " . (empty($config['pass']) ? '(فارغة)' : '***') . "<br>";
    echo "Database: {$config['name']}<br>";
    
    try {
        $conn = new mysqli($config['host'], $config['user'], $config['pass']);
        
        if ($conn->connect_error) {
            echo "<span style='color: red;'>❌ فشل الاتصال: " . $conn->connect_error . "</span><br>";
        } else {
            echo "<span style='color: green;'>✅ نجح الاتصال!</span><br>";
            
            // Check if database exists
            $db_exists = $conn->select_db($config['name']);
            if ($db_exists) {
                echo "<span style='color: green;'>✅ قاعدة البيانات موجودة!</span><br>";
                $working_config = $config;
                break;
            } else {
                echo "<span style='color: orange;'>⚠️ قاعدة البيانات غير موجودة - يمكن إنشاؤها</span><br>";
                $working_config = $config;
                $working_config['create_db'] = true;
            }
        }
        $conn->close();
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ خطأ: " . $e->getMessage() . "</span><br>";
    }
    echo "</p><hr>";
}

if ($working_config) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>✅ تم العثور على تكوين يعمل!</h3>";
    echo "<p><strong>الإعدادات الصحيحة:</strong></p>";
    echo "<ul>";
    echo "<li><strong>خادم قاعدة البيانات:</strong> {$working_config['host']}</li>";
    echo "<li><strong>اسم المستخدم:</strong> {$working_config['user']}</li>";
    echo "<li><strong>كلمة المرور:</strong> " . (empty($working_config['pass']) ? '(اتركها فارغة)' : $working_config['pass']) . "</li>";
    echo "<li><strong>اسم قاعدة البيانات:</strong> {$working_config['name']}</li>";
    echo "</ul>";
    
    if (isset($working_config['create_db'])) {
        echo "<p style='color: #856404;'><strong>ملاحظة:</strong> ستحتاج لإنشاء قاعدة البيانات أولاً.</p>";
        
        // Create database automatically
        try {
            $conn = new mysqli($working_config['host'], $working_config['user'], $working_config['pass']);
            $sql = "CREATE DATABASE IF NOT EXISTS `{$working_config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات تلقائياً!</p>";
            }
            $conn->close();
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في إنشاء قاعدة البيانات: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p><a href='install.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة لمعالج التثبيت</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ لم يتم العثور على تكوين يعمل</h3>";
    echo "<p><strong>الحلول المقترحة:</strong></p>";
    echo "<ol>";
    echo "<li><strong>استخدم SQLite بدلاً من MySQL:</strong><br>";
    echo "<a href='enable-sqlite-mode.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin: 5px 0; display: inline-block;'>تفعيل وضع SQLite</a></li>";
    echo "<li><strong>ثبّت XAMPP:</strong><br>";
    echo "حمّل من <a href='https://www.apachefriends.org/download.html' target='_blank'>هنا</a> وشغّل MySQL</li>";
    echo "<li><strong>ثبّت WAMP:</strong><br>";
    echo "حمّل من <a href='https://www.wampserver.com/en/' target='_blank'>هنا</a> كبديل لـ XAMPP</li>";
    echo "</ol>";
    echo "</div>";
}

// Check if XAMPP/WAMP is running
echo "<h3>فحص خوادم التطوير المحلية:</h3>";

// Check for XAMPP
if (file_exists('C:\\xampp\\mysql\\bin\\mysql.exe')) {
    echo "<p>✅ XAMPP مثبت في C:\\xampp</p>";
    if (is_dir('C:\\xampp\\mysql\\data')) {
        echo "<p>✅ مجلد بيانات MySQL موجود</p>";
    }
} else {
    echo "<p>❌ XAMPP غير مثبت أو غير موجود في المسار الافتراضي</p>";
}

// Check for WAMP
if (file_exists('C:\\wamp64\\bin\\mysql')) {
    echo "<p>✅ WAMP مثبت في C:\\wamp64</p>";
} else {
    echo "<p>❌ WAMP غير مثبت أو غير موجود في المسار الافتراضي</p>";
}

echo "<hr>";
echo "<h3>التوصية:</h3>";
echo "<p style='background: #cce5ff; padding: 15px; border-radius: 5px;'>";
echo "<strong>للتجربة السريعة:</strong> استخدم SQLite - لا يحتاج إعداد معقد<br>";
echo "<strong>للاختبار الكامل:</strong> ثبّت XAMPP وأعد المحاولة<br>";
echo "<strong>للإنتاج:</strong> استخدم MySQL على Hostinger";
echo "</p>";

echo "<p>";
echo "<a href='enable-sqlite-mode.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تجربة SQLite</a> ";
echo "<a href='install.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>معالج التثبيت</a> ";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
echo "</p>";
?>
