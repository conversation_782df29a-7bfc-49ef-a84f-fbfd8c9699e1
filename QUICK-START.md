# 🚀 دليل البدء السريع - تجربة النظام محلياً

## ⚡ الطريقة الأسرع (بدون MySQL)

### 1. تشغيل الخادم المحلي
```bash
# انقر نقراً مزدوجاً على الملف أو شغّل من Command Prompt
start-local-server.bat
```

### 2. تفعيل وضع SQLite
- اذهب إلى: http://localhost:8000/enable-sqlite-mode.php
- انقر "تفعيل وضع SQLite"

### 3. تشغيل النظام
- اذهب إلى: http://localhost:8000
- اسم المستخدم: `admin`
- كلمة المرور: `admin`

**🎉 مبروك! النظام يعمل الآن**

---

## 🛠️ الطريقة الكاملة (مع MySQL)

### 1. تثبيت XAMPP
- حمّل من: https://www.apachefriends.org/download.html
- ثبّت في `C:\xampp`

### 2. نسخ المشروع
```bash
# انسخ مجلد المشروع إلى
C:\xampp\htdocs\payroll
```

### 3. تشغيل XAMPP
- شغّل XAMPP Control Panel
- ابدأ Apache و MySQL

### 4. إنشاء قاعدة البيانات
- اذهب إلى: http://localhost/phpmyadmin
- أنشئ قاعدة بيانات: `payroll_system`
- استورد ملف `database.sql`

### 5. تشغيل النظام
- اذهب إلى: http://localhost/payroll
- اتبع معالج التثبيت

---

## 📱 اختبار سريع للوظائف

### ✅ قائمة الاختبار (5 دقائق):

1. **تسجيل الدخول** ✓
   - اسم المستخدم: admin
   - كلمة المرور: admin

2. **إضافة موظف** ✓
   - اذهب إلى الموظفين > إضافة موظف
   - املأ البيانات الأساسية
   - احفظ

3. **تسجيل حضور** ✓
   - اذهب إلى الحضور > تسجيل حضور
   - اختر الموظف والتاريخ
   - سجل الحضور

4. **حساب راتب** ✓
   - اذهب إلى المرتبات > حساب المرتبات
   - اختر الشهر والموظف
   - احسب الراتب

5. **عرض تقرير** ✓
   - اذهب إلى التقارير
   - اختر تقرير الحضور
   - اعرض التقرير

---

## 🔧 حل المشاكل السريع

### مشكلة: "لا يمكن الوصول للموقع"
**الحل:**
```bash
# تأكد من تشغيل الخادم
php -S localhost:8000

# أو استخدم منفذ آخر
php -S localhost:8080
```

### مشكلة: "Database connection failed"
**الحل:**
1. فعّل وضع SQLite: http://localhost:8000/enable-sqlite-mode.php
2. أو تأكد من تشغيل MySQL في XAMPP

### مشكلة: "Permission denied"
**الحل:**
```bash
# أعط صلاحيات للمجلدات
chmod 755 assets/uploads
chmod 755 backup
```

### مشكلة: الخطوط العربية
**الحل:**
- تأكد من حفظ الملفات بترميز UTF-8
- تحقق من إعدادات المتصفح

---

## 📊 بيانات تجريبية

### موظف تجريبي:
- **الرقم:** EMP001
- **الاسم:** أحمد محمد
- **المنصب:** مطور برمجيات
- **القسم:** تقنية المعلومات
- **الراتب:** 8000 ر.س

### حضور تجريبي:
- **التاريخ:** اليوم
- **وقت الحضور:** 08:00
- **وقت الانصراف:** 17:00
- **الحالة:** حاضر

---

## 🎯 نصائح للاختبار

### 1. اختبر على متصفحات مختلفة:
- Chrome ✓
- Firefox ✓
- Edge ✓

### 2. اختبر الاستجابة:
- Desktop (1920x1080) ✓
- Tablet (768x1024) ✓
- Mobile (375x667) ✓

### 3. اختبر الوظائف:
- إضافة/تعديل/حذف ✓
- رفع الملفات ✓
- طباعة التقارير ✓
- تسجيل الدخول/الخروج ✓

---

## 📋 قائمة مراجعة نهائية

### قبل الرفع على Hostinger:
- [ ] جميع الوظائف تعمل بشكل صحيح
- [ ] لا توجد أخطاء في Console
- [ ] التصميم يظهر بشكل صحيح
- [ ] الخطوط العربية تعمل
- [ ] رفع الملفات يعمل
- [ ] التقارير تُطبع بشكل صحيح
- [ ] الأمان يعمل (تسجيل دخول/خروج)

### ملفات للحذف قبل الرفع:
- [ ] `enable-sqlite-mode.php`
- [ ] `includes/config-local.php`
- [ ] `includes/use_sqlite.flag`
- [ ] `data/` (مجلد SQLite)
- [ ] `start-local-server.bat`

---

## 🚀 الخطوة التالية

بعد اختبار النظام محلياً بنجاح:

1. **احفظ نسخة احتياطية** من الإصدار المختبر
2. **احذف ملفات التطوير المحلي**
3. **اتبع دليل الرفع** في `DEPLOYMENT-GUIDE.md`
4. **ارفع على Hostinger** باستخدام `hostinger-setup.md`

---

## 📞 المساعدة

### إذا واجهت مشاكل:
1. راجع `local-setup.md` للتفاصيل الكاملة
2. تحقق من `health-check.php` لفحص النظام
3. راجع ملفات السجلات للأخطاء

### ملفات مفيدة:
- `README.md` - الدليل الشامل
- `FILES-INDEX.md` - فهرس الملفات
- `DEPLOYMENT-GUIDE.md` - دليل الرفع

---

**نصيحة:** اختبر النظام بدقة محلياً قبل الرفع لضمان عمل كل شيء بشكل مثالي! 🎯
