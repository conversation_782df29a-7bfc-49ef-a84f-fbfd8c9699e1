<?php
/**
 * System Health Check
 * 
 * This script performs a comprehensive health check of the payroll system
 * Use this to verify that everything is working correctly after deployment
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// Function to perform a health check
function health_check($name, $check_function, $critical = true) {
    $start_time = microtime(true);
    
    try {
        $result = $check_function();
        $status = $result ? 'PASS' : 'FAIL';
        $message = $result ? 'OK' : 'Failed';
        $class = $result ? 'text-green-600' : ($critical ? 'text-red-600' : 'text-yellow-600');
        $icon = $result ? 'fas fa-check-circle' : 'fas fa-times-circle';
    } catch (Exception $e) {
        $status = 'ERROR';
        $message = $e->getMessage();
        $class = 'text-red-600';
        $icon = 'fas fa-exclamation-triangle';
    }
    
    $execution_time = round((microtime(true) - $start_time) * 1000, 2);
    
    return [
        'name' => $name,
        'status' => $status,
        'message' => $message,
        'class' => $class,
        'icon' => $icon,
        'execution_time' => $execution_time,
        'critical' => $critical
    ];
}

// Health check functions
$checks = [];

// Database connectivity
$checks[] = health_check('Database Connection', function() {
    global $conn;
    return $conn && $conn->ping();
});

// Database tables
$checks[] = health_check('Database Tables', function() {
    global $conn;
    $required_tables = ['users', 'employees', 'attendance', 'salaries', 'activity_log', 'settings'];
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows === 0) {
            return false;
        }
    }
    return true;
});

// Admin user exists
$checks[] = health_check('Admin User', function() {
    global $conn;
    $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $row = $result->fetch_assoc();
    return $row['count'] > 0;
});

// File permissions
$checks[] = health_check('File Permissions', function() {
    $directories = ['assets/uploads', 'backup'];
    foreach ($directories as $dir) {
        if (!is_writable($dir)) {
            return false;
        }
    }
    return true;
});

// PHP extensions
$checks[] = health_check('PHP Extensions', function() {
    $required_extensions = ['mysqli', 'mbstring', 'json', 'session', 'fileinfo'];
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            return false;
        }
    }
    return true;
});

// Session functionality
$checks[] = health_check('Session Support', function() {
    return session_status() !== PHP_SESSION_DISABLED;
});

// Upload directory
$checks[] = health_check('Upload Directory', function() {
    $upload_dir = 'assets/uploads/employees';
    return file_exists($upload_dir) && is_writable($upload_dir);
});

// Backup directory
$checks[] = health_check('Backup Directory', function() {
    return file_exists('backup') && is_writable('backup');
});

// Security files
$checks[] = health_check('Security Configuration', function() {
    return file_exists('.htaccess') && file_exists('includes/security.php');
});

// Installation lock
$checks[] = health_check('Installation Lock', function() {
    return file_exists('includes/installed.lock');
}, false);

// Memory limit
$checks[] = health_check('Memory Limit', function() {
    $memory_limit = ini_get('memory_limit');
    $memory_bytes = return_bytes($memory_limit);
    return $memory_bytes >= 134217728; // 128MB
}, false);

// Upload limit
$checks[] = health_check('Upload Limit', function() {
    $upload_limit = ini_get('upload_max_filesize');
    $upload_bytes = return_bytes($upload_limit);
    return $upload_bytes >= 2097152; // 2MB
}, false);

// Helper function to convert memory limit to bytes
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

// Calculate overall health
$total_checks = count($checks);
$passed_checks = count(array_filter($checks, function($check) { return $check['status'] === 'PASS'; }));
$critical_failed = count(array_filter($checks, function($check) { 
    return $check['critical'] && $check['status'] !== 'PASS'; 
}));

$health_percentage = round(($passed_checks / $total_checks) * 100);
$overall_status = $critical_failed === 0 ? 'HEALTHY' : 'CRITICAL';
$overall_class = $critical_failed === 0 ? 'text-green-600' : 'text-red-600';

// System information
$system_info = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Operating System' => php_uname('s') . ' ' . php_uname('r'),
    'Memory Limit' => ini_get('memory_limit'),
    'Upload Max Size' => ini_get('upload_max_filesize'),
    'Post Max Size' => ini_get('post_max_size'),
    'Max Execution Time' => ini_get('max_execution_time') . 's',
    'Timezone' => date_default_timezone_get(),
    'Current Time' => date('Y-m-d H:i:s'),
];

// Database information
$db_info = [];
if ($conn) {
    $db_info = [
        'MySQL Version' => $conn->server_info,
        'Character Set' => $conn->character_set_name(),
        'Database Name' => DB_NAME,
        'Connection Status' => $conn->ping() ? 'Connected' : 'Disconnected',
    ];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص صحة النظام</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .health-meter {
            background: conic-gradient(
                #ef4444 0deg <?php echo (100 - $health_percentage) * 3.6; ?>deg,
                #10b981 <?php echo (100 - $health_percentage) * 3.6; ?>deg 360deg
            );
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-6xl">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                <i class="fas fa-heartbeat text-red-500 ml-2"></i>
                فحص صحة نظام إدارة الموظفين والرواتب
            </h1>
            
            <!-- Overall Health Status -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-32 h-32 rounded-full health-meter mb-4">
                    <div class="bg-white rounded-full w-24 h-24 flex items-center justify-center">
                        <span class="text-2xl font-bold <?php echo $overall_class; ?>">
                            <?php echo $health_percentage; ?>%
                        </span>
                    </div>
                </div>
                <h2 class="text-2xl font-bold <?php echo $overall_class; ?> mb-2">
                    <?php echo $overall_status === 'HEALTHY' ? 'النظام يعمل بشكل طبيعي' : 'يوجد مشاكل حرجة'; ?>
                </h2>
                <p class="text-gray-600">
                    <?php echo $passed_checks; ?> من <?php echo $total_checks; ?> فحوصات نجحت
                </p>
            </div>
            
            <!-- Health Checks Results -->
            <div class="mb-8">
                <h3 class="text-xl font-bold mb-4 text-gray-800">نتائج الفحوصات</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفحص</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرسالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">وقت التنفيذ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($checks as $check): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $check['name']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="<?php echo $check['class']; ?>">
                                            <i class="<?php echo $check['icon']; ?> ml-1"></i>
                                            <?php echo $check['status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $check['message']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $check['execution_time']; ?>ms
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 text-xs rounded-full <?php echo $check['critical'] ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                            <?php echo $check['critical'] ? 'حرج' : 'اختياري'; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- System Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-lg mb-3 text-gray-700">
                        <i class="fas fa-server ml-2"></i>معلومات النظام
                    </h3>
                    <dl class="space-y-2">
                        <?php foreach ($system_info as $key => $value): ?>
                            <div class="flex justify-between text-sm">
                                <dt class="font-medium text-gray-600"><?php echo $key; ?>:</dt>
                                <dd class="text-gray-900"><?php echo $value; ?></dd>
                            </div>
                        <?php endforeach; ?>
                    </dl>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-lg mb-3 text-gray-700">
                        <i class="fas fa-database ml-2"></i>معلومات قاعدة البيانات
                    </h3>
                    <dl class="space-y-2">
                        <?php foreach ($db_info as $key => $value): ?>
                            <div class="flex justify-between text-sm">
                                <dt class="font-medium text-gray-600"><?php echo $key; ?>:</dt>
                                <dd class="text-gray-900"><?php echo $value; ?></dd>
                            </div>
                        <?php endforeach; ?>
                    </dl>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="text-center">
                <button onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة الفحص
                </button>
                
                <?php if ($overall_status === 'HEALTHY'): ?>
                    <a href="index.php" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-home ml-2"></i>
                        الذهاب إلى النظام
                    </a>
                <?php else: ?>
                    <a href="DEPLOYMENT-GUIDE.md" target="_blank" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-book ml-2"></i>
                        دليل حل المشاكل
                    </a>
                <?php endif; ?>
            </div>
            
            <div class="mt-6 text-center text-sm text-gray-500">
                آخر فحص: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>
</body>
</html>
