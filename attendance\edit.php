<?php
/**
 * Edit Attendance Record Page
 * 
 * This file handles editing an attendance record
 */

// Include header
require_once '../includes/header.php';

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('معرّف سجل الحضور غير صالح', 'danger');
    redirect('index.php');
}

// Get attendance record ID
$attendance_id = (int)$_GET['id'];

// Initialize variables
$errors = [];

// Get attendance record details
$sql = "SELECT a.*, e.name as employee_name, e.employee_number 
        FROM attendance a 
        JOIN employees e ON a.employee_id = e.id 
        WHERE a.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $attendance_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if record exists
if ($result->num_rows === 0) {
    set_flash_message('سجل الحضور غير موجود', 'danger');
    redirect('index.php');
}

// Get attendance data
$attendance = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $attendance['date'] = sanitize($_POST['date']);
        $attendance['time_in'] = !empty($_POST['time_in']) ? sanitize($_POST['time_in']) : null;
        $attendance['time_out'] = !empty($_POST['time_out']) ? sanitize($_POST['time_out']) : null;
        $attendance['status'] = sanitize($_POST['status']);
        $attendance['overtime_hours'] = !empty($_POST['overtime_hours']) ? (float)$_POST['overtime_hours'] : 0;
        $attendance['notes'] = sanitize($_POST['notes']);
        
        // Validate input
        if (empty($attendance['date'])) {
            $errors['date'] = 'التاريخ مطلوب';
        } else {
            $date = DateTime::createFromFormat('Y-m-d', $attendance['date']);
            if (!$date || $date->format('Y-m-d') !== $attendance['date']) {
                $errors['date'] = 'التاريخ غير صالح';
            }
        }
        
        if (!empty($attendance['time_in']) && !empty($attendance['time_out'])) {
            // Check if time_out is after time_in
            $time_in = strtotime($attendance['time_in']);
            $time_out = strtotime($attendance['time_out']);
            
            if ($time_out <= $time_in) {
                $errors['time_out'] = 'وقت الانصراف يجب أن يكون بعد وقت الحضور';
            }
        }
        
        // If no errors, update attendance record
        if (empty($errors)) {
            $sql = "UPDATE attendance SET 
                    date = ?, 
                    time_in = ?, 
                    time_out = ?, 
                    status = ?, 
                    overtime_hours = ?, 
                    notes = ? 
                    WHERE id = ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssdsi", 
                $attendance['date'], 
                $attendance['time_in'], 
                $attendance['time_out'], 
                $attendance['status'], 
                $attendance['overtime_hours'], 
                $attendance['notes'], 
                $attendance_id
            );
            
            if ($stmt->execute()) {
                // Log activity
                log_activity('edit_attendance', 'تم تعديل سجل حضور الموظف: ' . $attendance['employee_name'] . ' ليوم ' . format_date($attendance['date']));
                
                // Set success message
                set_flash_message('تم تعديل سجل الحضور بنجاح', 'success');
                
                // Redirect to attendance list
                redirect('index.php');
            } else {
                $errors['db'] = 'حدث خطأ أثناء تعديل سجل الحضور. يرجى المحاولة مرة أخرى';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">تعديل سجل الحضور</h1>
    <a href="index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
        <i class="fas fa-arrow-right ml-2"></i> العودة إلى سجل الحضور
    </a>
</div>

<!-- Error Messages -->
<?php if (isset($errors['db']) || isset($errors['csrf'])): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">خطأ!</strong>
        <span class="block sm:inline"><?php echo $errors['db'] ?? $errors['csrf']; ?></span>
    </div>
<?php endif; ?>

<!-- Attendance Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="mb-6">
        <h2 class="text-xl font-semibold mb-2">معلومات الموظف</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-500 mb-1">الموظف</p>
                <p class="font-medium"><?php echo $attendance['employee_name']; ?></p>
            </div>
            <div>
                <p class="text-sm text-gray-500 mb-1">رقم الموظف</p>
                <p class="font-medium"><?php echo $attendance['employee_number']; ?></p>
            </div>
        </div>
    </div>
    
    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?id=' . $attendance_id); ?>">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Date -->
            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-1">التاريخ <span class="text-red-500">*</span></label>
                <input type="date" id="date" name="date" value="<?php echo $attendance['date']; ?>" class="w-full border <?php echo isset($errors['date']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                <?php if (isset($errors['date'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['date']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Time In -->
            <div>
                <label for="time_in" class="block text-sm font-medium text-gray-700 mb-1">وقت الحضور</label>
                <input type="time" id="time_in" name="time_in" value="<?php echo $attendance['time_in'] ? substr($attendance['time_in'], 0, 5) : ''; ?>" class="w-full border <?php echo isset($errors['time_in']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <?php if (isset($errors['time_in'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['time_in']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Time Out -->
            <div>
                <label for="time_out" class="block text-sm font-medium text-gray-700 mb-1">وقت الانصراف</label>
                <input type="time" id="time_out" name="time_out" value="<?php echo $attendance['time_out'] ? substr($attendance['time_out'], 0, 5) : ''; ?>" class="w-full border <?php echo isset($errors['time_out']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <?php if (isset($errors['time_out'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['time_out']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة <span class="text-red-500">*</span></label>
                <select id="status" name="status" class="w-full border <?php echo isset($errors['status']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                    <option value="present" <?php echo $attendance['status'] === 'present' ? 'selected' : ''; ?>>حاضر</option>
                    <option value="absent" <?php echo $attendance['status'] === 'absent' ? 'selected' : ''; ?>>غائب</option>
                    <option value="leave" <?php echo $attendance['status'] === 'leave' ? 'selected' : ''; ?>>إجازة</option>
                    <option value="late" <?php echo $attendance['status'] === 'late' ? 'selected' : ''; ?>>متأخر</option>
                </select>
                <?php if (isset($errors['status'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['status']; ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Overtime Hours -->
            <div>
                <label for="overtime_hours" class="block text-sm font-medium text-gray-700 mb-1">ساعات العمل الإضافية</label>
                <input type="number" id="overtime_hours" name="overtime_hours" value="<?php echo $attendance['overtime_hours']; ?>" step="0.5" min="0" class="w-full border <?php echo isset($errors['overtime_hours']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <?php if (isset($errors['overtime_hours'])): ?>
                    <p class="mt-1 text-sm text-red-500"><?php echo $errors['overtime_hours']; ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="mt-6">
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
            <textarea id="notes" name="notes" rows="3" class="w-full border <?php echo isset($errors['notes']) ? 'border-red-500' : 'border-gray-300'; ?> rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"><?php echo $attendance['notes']; ?></textarea>
            <?php if (isset($errors['notes'])): ?>
                <p class="mt-1 text-sm text-red-500"><?php echo $errors['notes']; ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Submit Button -->
        <div class="mt-6 flex justify-end">
            <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-save ml-2"></i> حفظ التغييرات
            </button>
        </div>
    </form>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
