<?php
/**
 * Dashboard Page
 * 
 * This is the main dashboard of the Employee Management System
 */

// Include header
require_once 'includes/header.php';

// Get statistics
$stats = [
    'total_employees' => 0,
    'active_employees' => 0,
    'present_today' => 0,
    'absent_today' => 0,
    'on_leave_today' => 0,
    'late_today' => 0
];

// Total employees
$sql = "SELECT COUNT(*) as total FROM employees";
$result = $conn->query($sql);
if ($result && $row = $result->fetch_assoc()) {
    $stats['total_employees'] = $row['total'];
}

// Active employees
$sql = "SELECT COUNT(*) as active FROM employees WHERE status = 'active'";
$result = $conn->query($sql);
if ($result && $row = $result->fetch_assoc()) {
    $stats['active_employees'] = $row['active'];
}

// Today's date
$today = date('Y-m-d');

// Present today
$sql = "SELECT COUNT(*) as present FROM attendance WHERE date = ? AND status = 'present'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();
if ($result && $row = $result->fetch_assoc()) {
    $stats['present_today'] = $row['present'];
}

// Absent today
$sql = "SELECT COUNT(*) as absent FROM attendance WHERE date = ? AND status = 'absent'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();
if ($result && $row = $result->fetch_assoc()) {
    $stats['absent_today'] = $row['absent'];
}

// On leave today
$sql = "SELECT COUNT(*) as on_leave FROM attendance WHERE date = ? AND status = 'leave'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();
if ($result && $row = $result->fetch_assoc()) {
    $stats['on_leave_today'] = $row['on_leave'];
}

// Late today
$sql = "SELECT COUNT(*) as late FROM attendance WHERE date = ? AND status = 'late'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $today);
$stmt->execute();
$result = $stmt->get_result();
if ($result && $row = $result->fetch_assoc()) {
    $stats['late_today'] = $row['late'];
}

// Get recent activities
$sql = "SELECT a.action, a.description, a.timestamp, u.name 
        FROM activity_log a 
        LEFT JOIN users u ON a.user_id = u.id 
        ORDER BY a.timestamp DESC 
        LIMIT 5";
$result = $conn->query($sql);
$activities = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $activities[] = $row;
    }
}

// Get recent employees
$sql = "SELECT id, employee_number, name, position, department, join_date 
        FROM employees 
        ORDER BY created_at DESC 
        LIMIT 5";
$result = $conn->query($sql);
$recent_employees = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $recent_employees[] = $row;
    }
}
?>

<h1 class="text-2xl font-bold mb-6">لوحة التحكم</h1>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Total Employees -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-blue-500">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">إجمالي الموظفين</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['total_employees']; ?></h2>
            </div>
            <div class="bg-blue-100 p-3 rounded-full">
                <i class="fas fa-users text-blue-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Active Employees -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-green-500">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">الموظفين النشطين</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['active_employees']; ?></h2>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
                <i class="fas fa-user-check text-green-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Present Today -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-primary">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">الحاضرين اليوم</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['present_today']; ?></h2>
            </div>
            <div class="bg-orange-100 p-3 rounded-full">
                <i class="fas fa-calendar-check text-primary text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Second Row of Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <!-- Absent Today -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-red-500">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">الغائبين اليوم</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['absent_today']; ?></h2>
            </div>
            <div class="bg-red-100 p-3 rounded-full">
                <i class="fas fa-calendar-times text-red-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- On Leave Today -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-purple-500">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">في إجازة اليوم</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['on_leave_today']; ?></h2>
            </div>
            <div class="bg-purple-100 p-3 rounded-full">
                <i class="fas fa-umbrella-beach text-purple-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Late Today -->
    <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-yellow-500">
        <div class="flex justify-between items-center">
            <div>
                <p class="text-sm text-gray-500 mb-1">المتأخرين اليوم</p>
                <h2 class="text-3xl font-bold"><?php echo $stats['late_today']; ?></h2>
            </div>
            <div class="bg-yellow-100 p-3 rounded-full">
                <i class="fas fa-clock text-yellow-500 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Access Buttons -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
    <a href="employees/add.php" class="bg-primary hover:bg-primary-dark text-white rounded-lg p-4 text-center transition duration-300">
        <i class="fas fa-user-plus text-2xl mb-2"></i>
        <p>إضافة موظف جديد</p>
    </a>
    <a href="attendance/record.php" class="bg-green-600 hover:bg-green-700 text-white rounded-lg p-4 text-center transition duration-300">
        <i class="fas fa-clipboard-check text-2xl mb-2"></i>
        <p>تسجيل الحضور</p>
    </a>
    <a href="salaries/calculate.php" class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-4 text-center transition duration-300">
        <i class="fas fa-calculator text-2xl mb-2"></i>
        <p>حساب المرتبات</p>
    </a>
    <a href="reports/index.php" class="bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-4 text-center transition duration-300">
        <i class="fas fa-chart-bar text-2xl mb-2"></i>
        <p>التقارير</p>
    </a>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Recent Activities -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-history ml-2 text-primary"></i> آخر النشاطات
        </h3>
        <?php if (empty($activities)): ?>
            <p class="text-gray-500 text-center py-4">لا توجد نشاطات حديثة</p>
        <?php else: ?>
            <ul class="divide-y">
                <?php foreach ($activities as $activity): ?>
                    <li class="py-3">
                        <div class="flex items-start">
                            <div class="bg-gray-100 p-2 rounded-full ml-3">
                                <i class="fas fa-user-circle text-gray-500"></i>
                            </div>
                            <div>
                                <p class="font-medium"><?php echo $activity['name'] ?? 'مستخدم'; ?></p>
                                <p class="text-sm text-gray-600"><?php echo $activity['description']; ?></p>
                                <p class="text-xs text-gray-500 mt-1"><?php echo format_date($activity['timestamp'], 'Y-m-d H:i'); ?></p>
                            </div>
                        </div>
                    </li>
                <?php endforeach; ?>
            </ul>
            <div class="mt-4 text-center">
                <a href="#" class="text-primary hover:underline">عرض كل النشاطات</a>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Recent Employees -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-user-plus ml-2 text-primary"></i> الموظفين الجدد
        </h3>
        <?php if (empty($recent_employees)): ?>
            <p class="text-gray-500 text-center py-4">لا يوجد موظفين جدد</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرقم</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المسمى الوظيفي</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التعيين</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recent_employees as $employee): ?>
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900"><?php echo $employee['employee_number']; ?></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                                    <a href="employees/view.php?id=<?php echo $employee['id']; ?>" class="text-primary hover:underline">
                                        <?php echo $employee['name']; ?>
                                    </a>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><?php echo $employee['position']; ?></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><?php echo format_date($employee['join_date']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-center">
                <a href="employees/index.php" class="text-primary hover:underline">عرض كل الموظفين</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
