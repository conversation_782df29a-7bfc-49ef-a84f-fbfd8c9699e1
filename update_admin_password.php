<?php
/**
 * Update Admin Password Script
 *
 * This script updates the admin password with proper hashing
 * Run this once after installation to ensure proper password hashing
 */

// Include configuration
require_once 'includes/config.php';
require_once 'includes/security.php';

// Check if this script should run
if (file_exists('includes/password_updated.lock')) {
    die('كلمة المرور محدثة بالفعل. احذف ملف includes/password_updated.lock لإعادة التشغيل.');
}

try {
    // Hash the default password properly
    $new_password = password_hash_custom('admin');

    // Update the admin user password
    $sql = "UPDATE users SET password = ? WHERE username = 'admin'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $new_password);

    if ($stmt->execute()) {
        // Create lock file to prevent re-running
        file_put_contents('includes/password_updated.lock', date('Y-m-d H:i:s'));

        echo "✅ تم تحديث كلمة مرور المدير بنجاح!<br>";
        echo "اسم المستخدم: admin<br>";
        echo "كلمة المرور: admin<br>";
        echo "<br><strong>مهم:</strong> غيّر كلمة المرور بعد تسجيل الدخول!<br>";
        echo "<br><a href='login.php'>الذهاب إلى صفحة تسجيل الدخول</a>";
    } else {
        throw new Exception("فشل في تحديث كلمة المرور: " . $conn->error);
    }

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
