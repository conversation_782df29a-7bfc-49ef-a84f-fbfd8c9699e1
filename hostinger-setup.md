# دليل رفع نظام إدارة الموظفين على استضافة Hostinger Business

## الخطوات التفصيلية للرفع والتثبيت

### 1. تحضير الملفات للرفع

#### أ. ضغط الملفات
1. اضغط جميع ملفات المشروع في ملف ZIP
2. تأكد من تضمين جميع المجلدات:
   - `assets/`
   - `attendance/`
   - `backup/`
   - `employees/`
   - `includes/`
   - `reports/`
   - `salaries/`
   - `settings/`
   - جميع الملفات في المجلد الرئيسي

#### ب. الملفات المطلوبة
- ✅ `.htaccess` (للأمان والحماية)
- ✅ `install.php` (معالج التثبيت)
- ✅ `database.sql` (هيكل قاعدة البيانات)
- ✅ `README.md` (دليل الاستخدام)
- ✅ جميع ملفات PHP

### 2. إعداد استضافة Hostinger

#### أ. الدخول إلى لوحة التحكم
1. ادخل إلى [hpanel.hostinger.com](https://hpanel.hostinger.com)
2. اختر موقعك من قائمة المواقع

#### ب. إنشاء قاعدة البيانات
1. اذهب إلى **"قواعد البيانات"** > **"MySQL Databases"**
2. انقر على **"إنشاء قاعدة بيانات جديدة"**
3. أدخل اسم قاعدة البيانات (مثال: `payroll_system`)
4. انقر **"إنشاء"**

#### ج. إنشاء مستخدم قاعدة البيانات
1. في نفس الصفحة، انقر **"إنشاء مستخدم جديد"**
2. أدخل اسم المستخدم وكلمة مرور قوية
3. اختر قاعدة البيانات التي أنشأتها
4. أعط المستخدم **جميع الصلاحيات**
5. احفظ البيانات:
   - اسم الخادم: `localhost`
   - اسم قاعدة البيانات: `u123456789_payroll` (مثال)
   - اسم المستخدم: `u123456789_payroll` (مثال)
   - كلمة المرور: (التي أدخلتها)

### 3. رفع الملفات

#### أ. استخدام File Manager
1. اذهب إلى **"الملفات"** > **"File Manager"**
2. ادخل إلى مجلد `public_html`
3. احذف أي ملفات افتراضية (index.html, etc.)
4. ارفع ملف ZIP الخاص بالمشروع
5. استخرج الملفات في `public_html`

#### ب. استخدام FTP (بديل)
```
Host: ftp.yourdomain.com
Username: your_ftp_username
Password: your_ftp_password
Port: 21
```

### 4. تشغيل معالج التثبيت

#### أ. الوصول للمعالج
1. اذهب إلى: `https://yourdomain.com/install.php`
2. ستظهر صفحة معالج التثبيت

#### ب. خطوات المعالج
**الخطوة 1: الترحيب**
- اقرأ المتطلبات
- انقر "ابدأ التثبيت"

**الخطوة 2: إعدادات قاعدة البيانات**
- خادم قاعدة البيانات: `localhost`
- اسم قاعدة البيانات: (الذي أنشأته في Hostinger)
- اسم المستخدم: (الذي أنشأته في Hostinger)
- كلمة المرور: (التي أدخلتها في Hostinger)
- انقر "اختبار الاتصال وحفظ الإعدادات"

**الخطوة 3: إنشاء قاعدة البيانات**
- انقر "إنشاء قاعدة البيانات"
- انتظر حتى اكتمال العملية

**الخطوة 4: إنهاء التثبيت**
- انقر "إنهاء التثبيت"

**الخطوة 5: اكتمال التثبيت**
- انقر "الذهاب إلى النظام"

### 5. تسجيل الدخول الأول

#### بيانات الدخول الافتراضية:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin`

#### ⚠️ مهم جداً:
1. غيّر كلمة المرور فوراً بعد تسجيل الدخول
2. احذف ملف `install.php` من الخادم
3. تأكد من عمل النسخ الاحتياطية

### 6. إعدادات الأمان الإضافية

#### أ. تفعيل SSL
1. في لوحة تحكم Hostinger، اذهب إلى **"SSL"**
2. فعّل **"Let's Encrypt SSL"** مجاناً
3. انتظر حتى يتم التفعيل (قد يستغرق بضع دقائق)

#### ب. إعدادات إضافية
1. تأكد من أن ملف `.htaccess` يعمل
2. اختبر الوصول إلى المجلدات المحمية
3. تأكد من عمل رفع الملفات

### 7. اختبار النظام

#### أ. اختبارات أساسية
- ✅ تسجيل الدخول والخروج
- ✅ إضافة موظف جديد
- ✅ تسجيل حضور
- ✅ عرض التقارير
- ✅ رفع صورة موظف

#### ب. اختبارات الأمان
- ✅ محاولة الوصول لملفات محمية
- ✅ اختبار حماية CSRF
- ✅ اختبار تشفير كلمات المرور

### 8. النسخ الاحتياطية

#### أ. إعداد النسخ الاحتياطية التلقائية
1. في Hostinger، اذهب إلى **"النسخ الاحتياطية"**
2. فعّل النسخ الاحتياطية اليومية
3. احفظ نسخة احتياطية يدوية أولى

#### ب. نسخ احتياطية يدوية
- قاعدة البيانات: استخدم phpMyAdmin
- الملفات: استخدم File Manager أو FTP

### 9. استكشاف الأخطاء

#### أ. مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من صحة بيانات قاعدة البيانات
- تأكد من أن المستخدم له صلاحيات كاملة

**صفحة بيضاء أو خطأ 500:**
- تحقق من ملف `error_log` في File Manager
- تأكد من أن PHP 7.4+ مفعل

**مشاكل في رفع الملفات:**
- تحقق من صلاحيات مجلد `assets/uploads`
- تأكد من أن `upload_max_filesize` كافي

**مشاكل في الخطوط العربية:**
- تأكد من أن قاعدة البيانات تستخدم `utf8mb4`
- تحقق من إعدادات PHP للـ charset

#### ب. ملفات السجلات
- `error_log` - أخطاء PHP
- `access_log` - سجل الوصول
- جدول `activity_log` - نشاطات النظام

### 10. تحسين الأداء

#### أ. إعدادات Hostinger
- فعّل **LiteSpeed Cache** إذا كان متاحاً
- استخدم **CloudFlare** للحماية والسرعة

#### ب. تحسينات النظام
- ضغط الصور قبل الرفع
- استخدام CDN للملفات الثابتة
- تحسين استعلامات قاعدة البيانات

---

## معلومات الدعم

### معلومات الاتصال بـ Hostinger:
- الدعم الفني: متاح 24/7 عبر Live Chat
- قاعدة المعرفة: [support.hostinger.com](https://support.hostinger.com)

### معلومات النظام:
- الإصدار: 1.0.0
- متطلبات PHP: 7.4+
- متطلبات MySQL: 5.7+

**ملاحظة:** احتفظ بنسخة من هذا الدليل ومن بيانات قاعدة البيانات في مكان آمن!
