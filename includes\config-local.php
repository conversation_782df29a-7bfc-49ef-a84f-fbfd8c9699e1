<?php
/**
 * Local Development Configuration
 * 
 * This file contains configuration for local development using SQLite
 * Use this for quick testing without MySQL setup
 */

// Check if we're in local development mode
$is_local_dev = file_exists(__DIR__ . '/use_sqlite.flag');

if ($is_local_dev) {
    // SQLite configuration for local development
    try {
        $sqlite_db = __DIR__ . '/../data/payroll_local.db';
        
        // Create data directory if it doesn't exist
        $data_dir = dirname($sqlite_db);
        if (!file_exists($data_dir)) {
            mkdir($data_dir, 0755, true);
        }
        
        // Create SQLite connection
        $pdo = new PDO('sqlite:' . $sqlite_db);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create tables if they don't exist
        create_sqlite_tables($pdo);
        
        // Set global connection for compatibility
        $GLOBALS['sqlite_connection'] = $pdo;
        $GLOBALS['using_sqlite'] = true;
        
        echo "<!-- Using SQLite for local development -->\n";
        
    } catch (Exception $e) {
        die("SQLite Error: " . $e->getMessage());
    }
} else {
    // Use regular MySQL configuration
    require_once 'config.php';
    $GLOBALS['using_sqlite'] = false;
}

/**
 * Create SQLite tables
 */
function create_sqlite_tables($pdo) {
    $tables = [
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                last_login DATETIME DEFAULT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT NULL
            )",
        
        'employees' => "
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_number VARCHAR(20) NOT NULL UNIQUE,
                name VARCHAR(100) NOT NULL,
                position VARCHAR(100) NOT NULL,
                department VARCHAR(100) NOT NULL,
                join_date DATE NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                phone VARCHAR(20) DEFAULT NULL,
                email VARCHAR(100) DEFAULT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'active',
                notes TEXT DEFAULT NULL,
                image VARCHAR(255) DEFAULT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT NULL
            )",
        
        'attendance' => "
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                date DATE NOT NULL,
                time_in TIME DEFAULT NULL,
                time_out TIME DEFAULT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'present',
                overtime_hours DECIMAL(5,2) DEFAULT 0.00,
                notes TEXT DEFAULT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
                UNIQUE(employee_id, date)
            )",
        
        'salaries' => "
            CREATE TABLE IF NOT EXISTS salaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                allowances DECIMAL(10,2) DEFAULT 0.00,
                deductions DECIMAL(10,2) DEFAULT 0.00,
                overtime_pay DECIMAL(10,2) DEFAULT 0.00,
                total_salary DECIMAL(10,2) NOT NULL,
                payment_date DATE DEFAULT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                notes TEXT DEFAULT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT NULL,
                FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
                UNIQUE(employee_id, month, year)
            )",
        
        'activity_log' => "
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER DEFAULT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT NOT NULL,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            )",
        
        'settings' => "
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                description VARCHAR(255) DEFAULT NULL,
                updated_at DATETIME DEFAULT NULL
            )"
    ];
    
    foreach ($tables as $table_name => $sql) {
        $pdo->exec($sql);
    }
    
    // Insert default admin user if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $password_hash = password_hash('admin', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, name, role, created_at) 
            VALUES ('admin', ?, 'مدير النظام', 'admin', datetime('now'))
        ");
        $stmt->execute([$password_hash]);
    }
    
    // Insert default settings if not exist
    $default_settings = [
        ['company_name', 'شركتي', 'اسم الشركة'],
        ['company_address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'],
        ['company_phone', '+966 12 345 6789', 'رقم هاتف الشركة'],
        ['company_email', '<EMAIL>', 'البريد الإلكتروني للشركة'],
        ['currency_symbol', 'ر.س', 'رمز العملة'],
        ['overtime_rate', '1.5', 'معدل العمل الإضافي'],
        ['work_hours_per_day', '8', 'ساعات العمل اليومية'],
        ['work_days_per_month', '22', 'أيام العمل الشهرية']
    ];
    
    foreach ($default_settings as $setting) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM settings WHERE setting_key = ?");
        $stmt->execute([$setting[0]]);
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
    }
}

/**
 * SQLite compatibility functions
 */
if (isset($GLOBALS['using_sqlite']) && $GLOBALS['using_sqlite']) {
    
    // Override mysqli functions for SQLite compatibility
    function sqlite_query($sql, $params = []) {
        global $sqlite_connection;
        try {
            $stmt = $sqlite_connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (Exception $e) {
            error_log("SQLite Query Error: " . $e->getMessage());
            return false;
        }
    }
    
    function sqlite_fetch_assoc($stmt) {
        if ($stmt) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        return false;
    }
    
    function sqlite_num_rows($stmt) {
        if ($stmt) {
            return $stmt->rowCount();
        }
        return 0;
    }
}

// System configuration (same as config.php)
date_default_timezone_set('Asia/Riyadh');

define('SYSTEM_NAME', 'نظام إدارة الموظفين والرواتب');
define('SYSTEM_VERSION', '1.0.0');
define('CURRENCY_SYMBOL', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('RECORDS_PER_PAGE', 10);

// File upload paths
define('UPLOAD_DIR', dirname(__DIR__) . '/assets/uploads/');
define('EMPLOYEE_IMAGES', UPLOAD_DIR . 'employees/');
define('BACKUP_DIR', UPLOAD_DIR . 'backups/');

// Create upload directories if they don't exist
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
if (!file_exists(EMPLOYEE_IMAGES)) {
    mkdir(EMPLOYEE_IMAGES, 0755, true);
}
if (!file_exists(BACKUP_DIR)) {
    mkdir(BACKUP_DIR, 0755, true);
}

?>
