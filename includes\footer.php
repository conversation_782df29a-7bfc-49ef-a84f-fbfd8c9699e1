<?php
/**
 * Footer File
 * 
 * This file contains the footer section of the Employee Management System
 */

// Check if user is logged in
if (is_logged_in() && basename($_SERVER['PHP_SELF']) !== 'login.php'):
?>
        </main>
    </div>
    
    <!-- Footer -->
    <footer class="bg-secondary text-white py-4 mt-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SYSTEM_NAME; ?> | الإصدار <?php echo SYSTEM_VERSION; ?></p>
        </div>
    </footer>
<?php endif; ?>

<!-- JavaScript -->
<script>
    // Toggle user menu
    document.addEventListener('DOMContentLoaded', function() {
        const userMenuButton = document.getElementById('userMenuButton');
        const userMenu = document.getElementById('userMenu');
        
        if (userMenuButton && userMenu) {
            userMenuButton.addEventListener('click', function() {
                userMenu.classList.toggle('hidden');
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });
        }
        
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.classList.remove('show');
                alert.classList.add('fade');
                
                setTimeout(function() {
                    alert.remove();
                }, 500);
            }, 5000);
        });
    });
</script>
</body>
</html>
