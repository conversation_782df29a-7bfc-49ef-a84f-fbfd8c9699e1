<?php
/**
 * Demo Page - Simple SQLite Demo
 * 
 * This page provides a quick demo using SQLite
 */

// Enable SQLite mode automatically
$flag_file = 'includes/use_sqlite.flag';
if (!file_exists($flag_file)) {
    file_put_contents($flag_file, date('Y-m-d H:i:s'));
}

// Include the simple configuration
require_once 'includes/config-simple.php';
require_once 'includes/functions.php';

// Handle login
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === 'admin' && $password === 'admin') {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['user_name'] = 'مدير النظام';
        $_SESSION['role'] = 'admin';
        
        header('Location: demo-dashboard.php');
        exit();
    } else {
        $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    }
}

// Check if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: demo-dashboard.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي - نظام إدارة الموظفين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-users text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-extrabold text-gray-900 mb-2">
                    عرض توضيحي سريع
                </h2>
                <p class="text-gray-600">
                    نظام إدارة الموظفين والرواتب
                </p>
                <div class="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <span class="text-sm">يعمل بـ SQLite - لا يحتاج MySQL</span>
                    </div>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="mt-8 space-y-6">
                <div class="rounded-md shadow-sm space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                            اسم المستخدم
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                            <input id="username" name="username" type="text" required 
                                   class="appearance-none rounded-lg relative block w-full pr-10 pl-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="admin" value="admin">
                        </div>
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input id="password" name="password" type="password" required
                                   class="appearance-none rounded-lg relative block w-full pr-10 pl-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="admin" value="admin">
                        </div>
                    </div>
                </div>
                
                <div>
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        دخول العرض التوضيحي
                    </button>
                </div>
            </form>
            
            <div class="text-center">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">بيانات تسجيل الدخول:</h3>
                    <p class="text-sm text-blue-700">
                        <strong>اسم المستخدم:</strong> admin<br>
                        <strong>كلمة المرور:</strong> admin
                    </p>
                </div>
            </div>
            
            <div class="text-center text-sm">
                <p class="text-gray-500">
                    <a href="index.php" class="text-blue-600 hover:text-blue-500">العودة للنظام الأصلي</a> |
                    <a href="enable-sqlite-mode.php" class="text-blue-600 hover:text-blue-500">إعدادات SQLite</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
