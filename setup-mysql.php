<?php
/**
 * MySQL Setup Helper
 * 
 * This script helps setup MySQL for local development
 */

$step = $_GET['step'] ?? 1;
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // Test MySQL connection and setup password
        $host = $_POST['host'] ?? 'localhost';
        $user = $_POST['user'] ?? 'root';
        $current_pass = $_POST['current_pass'] ?? '';
        $new_pass = $_POST['new_pass'] ?? '';
        
        try {
            // Try to connect with current password
            $conn = new mysqli($host, $user, $current_pass);
            
            if ($conn->connect_error) {
                throw new Exception("فشل الاتصال: " . $conn->connect_error);
            }
            
            // Set new password if provided
            if (!empty($new_pass)) {
                $sql = "ALTER USER '$user'@'$host' IDENTIFIED BY '$new_pass'";
                if ($conn->query($sql)) {
                    $message = "تم تعيين كلمة مرور جديدة بنجاح!";
                    $current_pass = $new_pass;
                } else {
                    $error = "فشل في تعيين كلمة المرور: " . $conn->error;
                }
            }
            
            // Create database
            $db_name = 'payroll_system';
            $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if ($conn->query($sql)) {
                $message .= " تم إنشاء قاعدة البيانات بنجاح!";
                
                // Store successful configuration
                $_SESSION['mysql_config'] = [
                    'host' => $host,
                    'user' => $user,
                    'pass' => $current_pass,
                    'name' => $db_name
                ];
                
                $step = 2;
            } else {
                $error = "فشل في إنشاء قاعدة البيانات: " . $conn->error;
            }
            
            $conn->close();
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif ($step == 2) {
        // Import database structure
        if (isset($_SESSION['mysql_config'])) {
            $config = $_SESSION['mysql_config'];
            
            try {
                $conn = new mysqli($config['host'], $config['user'], $config['pass'], $config['name']);
                
                if ($conn->connect_error) {
                    throw new Exception("فشل الاتصال: " . $conn->connect_error);
                }
                
                // Read and execute SQL file
                $sql_content = file_get_contents('database.sql');
                
                // Remove the database creation lines
                $sql_content = preg_replace('/CREATE DATABASE.*?;/i', '', $sql_content);
                $sql_content = preg_replace('/USE.*?;/i', '', $sql_content);
                
                $sql_statements = explode(';', $sql_content);
                
                foreach ($sql_statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        if (!$conn->query($statement)) {
                            throw new Exception("خطأ في تنفيذ الاستعلام: " . $conn->error);
                        }
                    }
                }
                
                $message = "تم إنشاء جداول قاعدة البيانات بنجاح!";
                $step = 3;
                
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    }
}

session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد MySQL للتطوير المحلي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
                <i class="fas fa-database text-blue-600 ml-2"></i>
                إعداد MySQL للتطوير المحلي
            </h1>
            
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-6">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?php echo ($step / 3) * 100; ?>%"></div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h3 class="text-xl font-bold mb-4">الخطوة 1: إعداد اتصال MySQL</h3>
                <form method="POST">
                    <input type="hidden" name="step" value="1">
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">خادم قاعدة البيانات</label>
                        <input type="text" name="host" value="localhost" required 
                               class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">اسم المستخدم</label>
                        <input type="text" name="user" value="root" required 
                               class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">كلمة المرور الحالية</label>
                        <input type="password" name="current_pass" 
                               class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="اتركها فارغة إذا لم تكن هناك كلمة مرور">
                        <p class="text-xs text-gray-500 mt-1">في XAMPP الافتراضي، كلمة المرور فارغة</p>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">كلمة مرور جديدة (اختياري)</label>
                        <input type="password" name="new_pass" 
                               class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="اتركها فارغة للاحتفاظ بالحالية">
                        <p class="text-xs text-gray-500 mt-1">يمكنك تعيين كلمة مرور جديدة أو تركها فارغة</p>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                        اختبار الاتصال وإنشاء قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <h3 class="text-xl font-bold mb-4">الخطوة 2: إنشاء جداول قاعدة البيانات</h3>
                <p class="mb-4">تم الاتصال بقاعدة البيانات بنجاح. سيتم الآن إنشاء الجداول المطلوبة.</p>
                <form method="POST">
                    <input type="hidden" name="step" value="2">
                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                        إنشاء جداول قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h3 class="text-xl font-bold mb-4 text-green-600">تم الإعداد بنجاح!</h3>
                <div class="bg-green-50 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold mb-2">إعدادات قاعدة البيانات:</h4>
                    <?php if (isset($_SESSION['mysql_config'])): ?>
                        <ul class="text-sm space-y-1">
                            <li><strong>الخادم:</strong> <?php echo $_SESSION['mysql_config']['host']; ?></li>
                            <li><strong>المستخدم:</strong> <?php echo $_SESSION['mysql_config']['user']; ?></li>
                            <li><strong>كلمة المرور:</strong> <?php echo empty($_SESSION['mysql_config']['pass']) ? '(فارغة)' : '***'; ?></li>
                            <li><strong>قاعدة البيانات:</strong> <?php echo $_SESSION['mysql_config']['name']; ?></li>
                        </ul>
                    <?php endif; ?>
                </div>
                
                <div class="text-center">
                    <a href="index.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ml-4">
                        <i class="fas fa-home ml-2"></i>
                        الذهاب إلى النظام
                    </a>
                    
                    <a href="install.php" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-cog ml-2"></i>
                        معالج التثبيت
                    </a>
                </div>
            <?php endif; ?>
            
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-bold text-blue-800 mb-2">نصائح:</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• تأكد من تشغيل XAMPP أو WAMP قبل البدء</li>
                    <li>• في XAMPP الافتراضي، كلمة مرور root فارغة</li>
                    <li>• يمكنك تعيين كلمة مرور جديدة للأمان</li>
                    <li>• إذا فشل الإعداد، جرب SQLite بدلاً من ذلك</li>
                </ul>
            </div>
            
            <div class="mt-4 text-center">
                <a href="demo.php" class="text-blue-600 hover:underline">
                    <i class="fas fa-play ml-1"></i>
                    أو جرب العرض التوضيحي بـ SQLite
                </a>
            </div>
        </div>
    </div>
</body>
</html>
