<?php
/**
 * Security Functions
 *
 * This file contains security-related functions for the payroll system
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 */
function check_login() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        header('Location: ' . get_base_url() . 'login.php');
        exit();
    }
}

/**
 * Check user role
 */
function check_role($required_role) {
    check_login();

    $user_role = $_SESSION['role'] ?? 'user';
    $roles_hierarchy = ['user' => 1, 'supervisor' => 2, 'admin' => 3];

    if ($roles_hierarchy[$user_role] < $roles_hierarchy[$required_role]) {
        header('HTTP/1.0 403 Forbidden');
        die('ليس لديك صلاحية للوصول إلى هذه الصفحة');
    }
}

/**
 * Enhanced CSRF token verification with timing attack protection
 */
function verify_csrf_token_secure($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize input data
 */
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Validate phone number (Saudi format)
 */
function validate_phone($phone) {
    // Remove spaces and special characters
    $phone = preg_replace('/[^0-9+]/', '', $phone);

    // Check Saudi phone number patterns
    $patterns = [
        '/^(\+966|966|0)?5[0-9]{8}$/',  // Mobile
        '/^(\+966|966|0)?1[0-9]{7}$/',  // Landline
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return true;
        }
    }

    return false;
}

/**
 * Enhanced password hashing with additional security
 */
function hash_password_secure($password, $cost = 12) {
    return password_hash($password, PASSWORD_DEFAULT, ['cost' => $cost]);
}

/**
 * Enhanced password verification
 */
function verify_password_secure($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random password
 */
function generate_password($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

/**
 * Log security events
 */
function log_security_event($event, $description, $user_id = null) {
    global $conn;

    if (!$user_id && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    $sql = "INSERT INTO activity_log (user_id, action, description, timestamp) VALUES (?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $user_id, $event, $description);
    $stmt->execute();
}

/**
 * Rate limiting for login attempts
 */
function check_rate_limit($identifier, $max_attempts = 5, $time_window = 900) {
    $cache_file = sys_get_temp_dir() . '/payroll_rate_limit_' . md5($identifier);

    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);

        // Clean old attempts
        $data['attempts'] = array_filter($data['attempts'], function($time) use ($time_window) {
            return (time() - $time) < $time_window;
        });

        if (count($data['attempts']) >= $max_attempts) {
            return false;
        }
    } else {
        $data = ['attempts' => []];
    }

    $data['attempts'][] = time();
    file_put_contents($cache_file, json_encode($data));

    return true;
}

/**
 * Get base URL
 */
function get_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path . '/';
}

/**
 * Secure file upload
 */
function secure_file_upload($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif'], $max_size = 2097152) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'error' => 'لم يتم رفع الملف بشكل صحيح'];
    }

    // Check file size
    if ($file['size'] > $max_size) {
        return ['success' => false, 'error' => 'حجم الملف كبير جداً'];
    }

    // Check file type
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'error' => 'نوع الملف غير مسموح'];
    }

    // Generate secure filename
    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;

    return ['success' => true, 'filename' => $new_filename];
}

/**
 * Clean old temporary files
 */
function cleanup_temp_files() {
    $temp_dir = sys_get_temp_dir();
    $files = glob($temp_dir . '/payroll_*');

    foreach ($files as $file) {
        if (filemtime($file) < (time() - 3600)) { // 1 hour old
            unlink($file);
        }
    }
}

// Auto-cleanup on script execution
register_shutdown_function('cleanup_temp_files');
?>
