<?php
/**
 * Attendance List Page
 * 
 * This file displays a list of attendance records
 */

// Include header
require_once '../includes/header.php';

// Initialize variables
$employee_id = isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0;
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : date('Y-m-01'); // First day of current month
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : date('Y-m-t'); // Last day of current month
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT a.*, e.name as employee_name, e.employee_number 
        FROM attendance a 
        JOIN employees e ON a.employee_id = e.id 
        WHERE 1=1";
$count_sql = "SELECT COUNT(*) as total 
              FROM attendance a 
              JOIN employees e ON a.employee_id = e.id 
              WHERE 1=1";
$params = [];
$types = "";

if ($employee_id > 0) {
    $sql .= " AND a.employee_id = ?";
    $count_sql .= " AND a.employee_id = ?";
    $params[] = $employee_id;
    $types .= "i";
}

if (!empty($date_from)) {
    $sql .= " AND a.date >= ?";
    $count_sql .= " AND a.date >= ?";
    $params[] = $date_from;
    $types .= "s";
}

if (!empty($date_to)) {
    $sql .= " AND a.date <= ?";
    $count_sql .= " AND a.date <= ?";
    $params[] = $date_to;
    $types .= "s";
}

if (!empty($status)) {
    $sql .= " AND a.status = ?";
    $count_sql .= " AND a.status = ?";
    $params[] = $status;
    $types .= "s";
}

// Add order by and limit
$sql .= " ORDER BY a.date DESC, e.name ASC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$types .= "ii";

// Get total records
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_types = substr($types, 0, -2); // Remove the 'ii' for limit and offset
    $count_params = array_slice($params, 0, -2); // Remove limit and offset params
    
    if (!empty($count_params)) {
        $count_stmt->bind_param($count_types, ...$count_params);
    }
}
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$count_row = $count_result->fetch_assoc();
$total_records = $count_row['total'];
$total_pages = ceil($total_records / $limit);

// Get attendance records
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$attendance_records = [];
while ($row = $result->fetch_assoc()) {
    $attendance_records[] = $row;
}

// Get employees for filter
$emp_sql = "SELECT id, name, employee_number FROM employees ORDER BY name";
$emp_result = $conn->query($emp_sql);
$employees = [];
if ($emp_result) {
    while ($row = $emp_result->fetch_assoc()) {
        $employees[] = $row;
    }
}

// Get employee name if filtered by employee
$employee_name = '';
if ($employee_id > 0) {
    foreach ($employees as $emp) {
        if ($emp['id'] == $employee_id) {
            $employee_name = $emp['name'];
            break;
        }
    }
}
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">
        <?php echo $employee_id > 0 ? 'سجل حضور الموظف: ' . $employee_name : 'سجل الحضور'; ?>
    </h1>
    <div class="flex space-x-2 space-x-reverse">
        <a href="record.php" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-clipboard-check ml-2"></i> تسجيل الحضور
        </a>
        <a href="report.php" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-chart-bar ml-2"></i> تقارير الحضور
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
            <label for="employee_id" class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
            <select id="employee_id" name="employee_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <?php foreach ($employees as $emp): ?>
                    <option value="<?php echo $emp['id']; ?>" <?php echo $employee_id == $emp['id'] ? 'selected' : ''; ?>>
                        <?php echo $emp['name'] . ' (' . $emp['employee_number'] . ')'; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div>
            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
            <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
        </div>
        
        <div>
            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
            <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
        </div>
        
        <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
            <select id="status" name="status" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <option value="present" <?php echo $status === 'present' ? 'selected' : ''; ?>>حاضر</option>
                <option value="absent" <?php echo $status === 'absent' ? 'selected' : ''; ?>>غائب</option>
                <option value="leave" <?php echo $status === 'leave' ? 'selected' : ''; ?>>إجازة</option>
                <option value="late" <?php echo $status === 'late' ? 'selected' : ''; ?>>متأخر</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-search ml-2"></i> بحث
            </button>
            <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mr-2 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                <i class="fas fa-redo ml-2"></i> إعادة تعيين
            </a>
        </div>
    </form>
</div>

<!-- Attendance Records -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b">
        <h2 class="text-xl font-semibold">سجلات الحضور</h2>
        <p class="text-gray-500 text-sm">إجمالي السجلات: <?php echo $total_records; ?></p>
    </div>
    
    <?php if (empty($attendance_records)): ?>
        <div class="p-6 text-center">
            <p class="text-gray-500">لا توجد سجلات حضور للعرض</p>
        </div>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الموظف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            وقت الحضور
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            وقت الانصراف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ساعات العمل الإضافية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($attendance_records as $record): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo format_date($record['date']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <a href="../employees/view.php?id=<?php echo $record['employee_id']; ?>" class="text-primary hover:underline">
                                        <?php echo $record['employee_name']; ?>
                                    </a>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <?php echo $record['employee_number']; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $record['time_in'] ? format_time($record['time_in']) : '-'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $record['time_out'] ? format_time($record['time_out']) : '-'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $status_class = '';
                                $status_text = '';
                                
                                switch ($record['status']) {
                                    case 'present':
                                        $status_class = 'bg-green-100 text-green-800';
                                        $status_text = 'حاضر';
                                        break;
                                    case 'absent':
                                        $status_class = 'bg-red-100 text-red-800';
                                        $status_text = 'غائب';
                                        break;
                                    case 'leave':
                                        $status_class = 'bg-purple-100 text-purple-800';
                                        $status_text = 'إجازة';
                                        break;
                                    case 'late':
                                        $status_class = 'bg-yellow-100 text-yellow-800';
                                        $status_text = 'متأخر';
                                        break;
                                }
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $record['overtime_hours'] > 0 ? $record['overtime_hours'] . ' ساعة' : '-'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="edit.php?id=<?php echo $record['id']; ?>" class="text-yellow-600 hover:text-yellow-900 ml-3">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="delete.php?id=<?php echo $record['id']; ?>" class="text-red-600 hover:text-red-900 ml-3" onclick="return confirm('هل أنت متأكد من حذف هذا السجل؟');">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        عرض <?php echo count($attendance_records); ?> من <?php echo $total_records; ?> سجل
                    </div>
                    <div class="flex space-x-1 space-x-reverse">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&employee_id=<?php echo $employee_id; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&status=<?php echo $status; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                السابق
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&employee_id=<?php echo $employee_id; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&status=<?php echo $status; ?>" class="px-3 py-1 <?php echo $i === $page ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-gray-50'; ?> border border-gray-300 rounded-md text-sm font-medium">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&employee_id=<?php echo $employee_id; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&status=<?php echo $status; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                التالي
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
