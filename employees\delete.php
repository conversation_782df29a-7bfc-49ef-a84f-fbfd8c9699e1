<?php
/**
 * Delete Employee Page
 * 
 * This file handles deleting an employee
 */

// Include functions file
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('معرّف الموظف غير صالح', 'danger');
    redirect('index.php');
}

// Get employee ID
$employee_id = (int)$_GET['id'];

// Get employee details
$sql = "SELECT id, name, image FROM employees WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $employee_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if employee exists
if ($result->num_rows === 0) {
    set_flash_message('الموظف غير موجود', 'danger');
    redirect('index.php');
}

// Get employee data
$employee = $result->fetch_assoc();

// Delete employee
$sql = "DELETE FROM employees WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $employee_id);

if ($stmt->execute()) {
    // Delete employee image if exists
    if (!empty($employee['image'])) {
        $image_path = EMPLOYEE_IMAGES . $employee['image'];
        if (file_exists($image_path)) {
            unlink($image_path);
        }
    }
    
    // Log activity
    log_activity('delete_employee', 'تم حذف الموظف: ' . $employee['name']);
    
    // Set success message
    set_flash_message('تم حذف الموظف بنجاح', 'success');
} else {
    // Set error message
    set_flash_message('حدث خطأ أثناء حذف الموظف', 'danger');
}

// Redirect to employees list
redirect('index.php');
?>
