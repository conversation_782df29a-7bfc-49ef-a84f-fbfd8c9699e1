<?php
/**
 * Simple Configuration for Local Development
 * 
 * This file provides a simple setup for local testing
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if SQLite mode is enabled
$sqlite_flag = __DIR__ . '/use_sqlite.flag';
$use_sqlite = file_exists($sqlite_flag);

if ($use_sqlite) {
    // SQLite Configuration
    try {
        $sqlite_db = __DIR__ . '/../data/payroll_local.db';
        
        // Create data directory if it doesn't exist
        $data_dir = dirname($sqlite_db);
        if (!file_exists($data_dir)) {
            mkdir($data_dir, 0755, true);
        }
        
        // Create SQLite connection using PDO
        $pdo = new PDO('sqlite:' . $sqlite_db);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create tables and default data
        create_sqlite_tables($pdo);
        
        // Set global variables for compatibility
        $GLOBALS['pdo'] = $pdo;
        $GLOBALS['using_sqlite'] = true;
        
        // Create a fake mysqli object for compatibility
        $conn = new SQLiteCompat($pdo);
        
    } catch (Exception $e) {
        die("SQLite Error: " . $e->getMessage());
    }
} else {
    // MySQL Configuration
    define('DB_HOST', 'localhost');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_NAME', 'payroll_system');
    
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        $conn->set_charset("utf8mb4");
        $GLOBALS['using_sqlite'] = false;
        
    } catch (Exception $e) {
        die("MySQL Error: " . $e->getMessage() . "<br><br><a href='enable-sqlite-mode.php'>جرب SQLite بدلاً من ذلك</a>");
    }
}

// System configuration
date_default_timezone_set('Asia/Riyadh');
define('SYSTEM_NAME', 'نظام إدارة الموظفين والرواتب');
define('SYSTEM_VERSION', '1.0.0');
define('CURRENCY_SYMBOL', 'ر.س');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('RECORDS_PER_PAGE', 10);

// File upload paths
define('UPLOAD_DIR', dirname(__DIR__) . '/assets/uploads/');
define('EMPLOYEE_IMAGES', UPLOAD_DIR . 'employees/');
define('BACKUP_DIR', UPLOAD_DIR . 'backups/');

// Create upload directories
if (!file_exists(UPLOAD_DIR)) mkdir(UPLOAD_DIR, 0755, true);
if (!file_exists(EMPLOYEE_IMAGES)) mkdir(EMPLOYEE_IMAGES, 0755, true);
if (!file_exists(BACKUP_DIR)) mkdir(BACKUP_DIR, 0755, true);

/**
 * Create SQLite tables and default data
 */
function create_sqlite_tables($pdo) {
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            name VARCHAR(100) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'user',
            last_login DATETIME DEFAULT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL
        )
    ");
    
    // Create employees table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_number VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            position VARCHAR(100) NOT NULL,
            department VARCHAR(100) NOT NULL,
            join_date DATE NOT NULL,
            basic_salary DECIMAL(10,2) NOT NULL,
            phone VARCHAR(20) DEFAULT NULL,
            email VARCHAR(100) DEFAULT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'active',
            notes TEXT DEFAULT NULL,
            image VARCHAR(255) DEFAULT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL
        )
    ");
    
    // Create attendance table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            date DATE NOT NULL,
            time_in TIME DEFAULT NULL,
            time_out TIME DEFAULT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'present',
            overtime_hours DECIMAL(5,2) DEFAULT 0.00,
            notes TEXT DEFAULT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
            UNIQUE(employee_id, date)
        )
    ");
    
    // Create salaries table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS salaries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            month INTEGER NOT NULL,
            year INTEGER NOT NULL,
            basic_salary DECIMAL(10,2) NOT NULL,
            allowances DECIMAL(10,2) DEFAULT 0.00,
            deductions DECIMAL(10,2) DEFAULT 0.00,
            overtime_pay DECIMAL(10,2) DEFAULT 0.00,
            total_salary DECIMAL(10,2) NOT NULL,
            payment_date DATE DEFAULT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            notes TEXT DEFAULT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL,
            FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
            UNIQUE(employee_id, month, year)
        )
    ");
    
    // Create activity_log table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER DEFAULT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )
    ");
    
    // Create settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            description VARCHAR(255) DEFAULT NULL,
            updated_at DATETIME DEFAULT NULL
        )
    ");
    
    // Insert default admin user if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $password_hash = password_hash('admin', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, name, role, created_at) 
            VALUES ('admin', ?, 'مدير النظام', 'admin', datetime('now'))
        ");
        $stmt->execute([$password_hash]);
    }
    
    // Insert default settings
    $default_settings = [
        ['company_name', 'شركتي', 'اسم الشركة'],
        ['company_address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'],
        ['company_phone', '+966 12 345 6789', 'رقم هاتف الشركة'],
        ['company_email', '<EMAIL>', 'البريد الإلكتروني للشركة'],
        ['currency_symbol', 'ر.س', 'رمز العملة'],
        ['overtime_rate', '1.5', 'معدل العمل الإضافي'],
        ['work_hours_per_day', '8', 'ساعات العمل اليومية'],
        ['work_days_per_month', '22', 'أيام العمل الشهرية']
    ];
    
    foreach ($default_settings as $setting) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM settings WHERE setting_key = ?");
        $stmt->execute([$setting[0]]);
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
    }
}

/**
 * SQLite Compatibility Class
 */
class SQLiteCompat {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function query($sql) {
        try {
            return $this->pdo->query($sql);
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function prepare($sql) {
        return new SQLiteStmtCompat($this->pdo->prepare($sql));
    }
    
    public function ping() {
        return true;
    }
    
    public function set_charset($charset) {
        return true;
    }
    
    public $connect_error = null;
}

/**
 * SQLite Statement Compatibility Class
 */
class SQLiteStmtCompat {
    private $stmt;
    
    public function __construct($stmt) {
        $this->stmt = $stmt;
    }
    
    public function bind_param($types, ...$params) {
        for ($i = 0; $i < count($params); $i++) {
            $this->stmt->bindValue($i + 1, $params[$i]);
        }
        return true;
    }
    
    public function execute() {
        return $this->stmt->execute();
    }
    
    public function get_result() {
        return new SQLiteResultCompat($this->stmt);
    }
}

/**
 * SQLite Result Compatibility Class
 */
class SQLiteResultCompat {
    private $stmt;
    
    public function __construct($stmt) {
        $this->stmt = $stmt;
    }
    
    public function fetch_assoc() {
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public $num_rows = 0;
}

?>
