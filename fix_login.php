<?php
/**
 * Fix Login Script
 * 
 * This script fixes the login issue by recreating the admin user
 */

// Database connection settings
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'payroll_system';

// Connect to database
try {
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "Connected to database successfully.<br>";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows == 0) {
        echo "Users table does not exist. Creating table...<br>";
        
        // Create users table
        $sql = "CREATE TABLE IF NOT EXISTS `users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `name` varchar(100) NOT NULL,
          `role` enum('admin','supervisor','user') NOT NULL DEFAULT 'user',
          `last_login` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        if ($conn->query($sql) === TRUE) {
            echo "Users table created successfully.<br>";
        } else {
            echo "Error creating users table: " . $conn->error . "<br>";
        }
    } else {
        echo "Users table exists.<br>";
    }
    
    // Delete existing admin user if exists
    $sql = "DELETE FROM users WHERE username = 'admin'";
    if ($conn->query($sql) === TRUE) {
        echo "Existing admin user deleted.<br>";
    } else {
        echo "Error deleting admin user: " . $conn->error . "<br>";
    }
    
    // Create new admin user with password 'admin'
    // Using bcrypt hash for 'admin'
    $password_hash = '$2y$12$Ht1EiKHFTr5Hy.Oa9Yx.eeJjH3hRZ9E/oyK2jlXNr7yQfDmYkE3Oe';
    $sql = "INSERT INTO users (username, password, name, role, created_at) 
            VALUES ('admin', '$password_hash', 'مدير النظام', 'admin', NOW())";
    
    if ($conn->query($sql) === TRUE) {
        echo "Admin user created successfully.<br>";
        echo "Username: admin<br>";
        echo "Password: admin<br>";
    } else {
        echo "Error creating admin user: " . $conn->error . "<br>";
    }
    
    // Close connection
    $conn->close();
    echo "Database connection closed.<br>";
    echo "Please try logging in with username 'admin' and password 'admin'.<br>";
    
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}
?>
