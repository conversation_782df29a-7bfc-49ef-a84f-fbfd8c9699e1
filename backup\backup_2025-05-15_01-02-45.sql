<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين والرواتب</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#FF6B00', // Orange
                            dark: '#E05A00',
                            light: '#FF8C3F'
                        },
                        secondary: {
                            DEFAULT: '#1F2937', // Dark Gray
                            dark: '#111827',
                            light: '#374151'
                        }
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo (Arabic) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }

        /* RTL specific adjustments */
        .rtl-flip {
            transform: scaleX(-1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
        <!-- Navigation -->
    <nav class="bg-secondary text-white shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center">
                    <a href="/newpayroll/index.php" class="text-xl font-bold text-primary">
                        نظام إدارة الموظفين والرواتب                    </a>
                </div>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="relative">
                        <button id="userMenuButton" class="flex items-center space-x-2 space-x-reverse focus:outline-none">
                            <span class="text-sm">مدير النظام</span>
                            <i class="fas fa-user-circle text-xl"></i>
                        </button>
                        <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden z-10">
                            <a href="/newpayroll/settings/profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog ml-2"></i> الملف الشخصي
                            </a>
                            <a href="/newpayroll/logout.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar and Content -->
    <div class="container mx-auto px-4 py-8 flex flex-wrap">
        <!-- Sidebar -->
        <aside class="w-full md:w-1/5 bg-white rounded-lg shadow-md p-4 ml-6">
            <ul class="space-y-2">
                <li>
                    <a href="/newpayroll/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-tachometer-alt ml-2"></i> لوحة التحكم
                    </a>
                </li>
                <li>
                    <a href="/newpayroll/employees/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-users ml-2"></i> الموظفين
                    </a>
                </li>
                <li>
                    <a href="/newpayroll/attendance/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-calendar-check ml-2"></i> الحضور
                    </a>
                </li>
                <li>
                    <a href="/newpayroll/salaries/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-money-bill-wave ml-2"></i> المرتبات
                    </a>
                </li>
                <li>
                    <a href="/newpayroll/reports/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-chart-bar ml-2"></i> التقارير
                    </a>
                </li>
                <li>
                    <a href="/newpayroll/settings/index.php" class="block py-2 px-4 rounded-md hover:bg-gray-100">
                        <i class="fas fa-cog ml-2"></i> الإعدادات
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="w-full md:w-4/5 bg-white rounded-lg shadow-md p-6">
                -- نسخة احتياطية لقاعدة البيانات payroll_system
-- تاريخ الإنشاء: 2025-05-15 01:02:45
-- خادم: localhost
-- PHP نسخة: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------

CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `activity_log` VALUES
('1','2','login','تم تسجيل الدخول بنجاح','2025-05-15 00:16:52'),
('2','2','logout','تم تسجيل الخروج بنجاح','2025-05-15 00:20:59'),
('3','2','login','تم تسجيل الدخول بنجاح','2025-05-15 00:21:32'),
('4','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:27:21'),
('5','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:28:05'),
('6','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:28:36'),
('7','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:28:44'),
('8','2','add_employee','تمت إضافة موظف جديد: علي محمد ابراهيم','2025-05-15 00:29:56'),
('9','2','add_attendance','تم تسجيل حضور الموظف: علي محمد ابراهيم ليوم 2025-05-15','2025-05-15 00:32:24'),
('10','2','add_employee','تمت إضافة موظف جديد: عمرو محمد','2025-05-15 00:33:22'),
('11','2','add_employee','تمت إضافة موظف جديد: محمود نجيب','2025-05-15 00:34:24'),
('12','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:35:11'),
('13','2','bulk_attendance','تم تسجيل حضور 3 موظف ليوم 2025-05-15','2025-05-15 00:36:22'),
('14','2','calculate_salary','تم حساب راتب الموظف: علي محمد ابراهيم لشهر مايو 2025','2025-05-15 00:37:20'),
('15','2','calculate_salary','تم حساب راتب الموظف: عمرو محمد لشهر مايو 2025','2025-05-15 00:37:20'),
('16','2','calculate_salary','تم حساب راتب الموظف: محمود نجيب لشهر مايو 2025','2025-05-15 00:37:20'),
('17','2','update_settings','تم تحديث إعدادات النظام','2025-05-15 00:40:06');

-- --------------------------------------------------------

CREATE TABLE `attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `time_in` time DEFAULT NULL,
  `time_out` time DEFAULT NULL,
  `status` enum('present','absent','leave','late') NOT NULL DEFAULT 'present',
  `overtime_hours` decimal(5,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_date` (`employee_id`,`date`),
  CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `attendance` VALUES
('1','1','2025-05-15','08:01:00','05:31:00','present','0.00','','2025-05-15 00:32:23'),
('2','2','2025-05-15',NULL,NULL,'leave','0.00',NULL,'2025-05-15 00:36:22