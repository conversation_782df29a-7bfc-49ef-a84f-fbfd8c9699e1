# نظام إدارة الموظفين والرواتب

نظام ويب متكامل لإدارة الموظفين وتسجيل الحضور وحساب المرتبات مع واجهة استخدام عربية أنيقة وسهلة الاستخدام.

## المميزات

### إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- رفع صور الموظفين
- تتبع تواريخ التعيين والمناصب
- إدارة الأقسام والمسميات الوظيفية

### نظام الحضور والانصراف
- تسجيل حضور وانصراف الموظفين
- تتبع التأخير والغياب
- إدارة الإجازات
- حساب ساعات العمل الإضافية

### نظام الرواتب
- حساب الرواتب الشهرية تلقائياً
- إدارة البدلات والخصومات
- تتبع المدفوعات
- طباعة كشوف المرتبات

### التقارير والإحصائيات
- تقارير الحضور والغياب
- تقارير الرواتب
- إحصائيات شاملة
- تصدير التقارير

### الأمان والحماية
- نظام تسجيل دخول آمن
- صلاحيات متعددة المستويات
- تشفير كلمات المرور
- حماية من الهجمات الأمنية

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- مساحة تخزين: 100 ميجابايت على الأقل

## التثبيت على استضافة Hostinger

### الخطوة 1: تحضير الملفات
1. قم بتحميل جميع ملفات المشروع إلى مجلد `public_html` في استضافتك
2. تأكد من رفع جميع المجلدات والملفات بما في ذلك المجلدات المخفية

### الخطوة 2: إنشاء قاعدة البيانات
1. ادخل إلى لوحة تحكم Hostinger
2. اذهب إلى قسم "قواعد البيانات" > "MySQL Databases"
3. أنشئ قاعدة بيانات جديدة (مثال: `u123456789_payroll`)
4. أنشئ مستخدم جديد وأعطه جميع الصلاحيات على قاعدة البيانات
5. احفظ اسم قاعدة البيانات واسم المستخدم وكلمة المرور

### الخطوة 3: فحص المتطلبات (اختياري)
1. اذهب إلى: `https://yourdomain.com/check-requirements.php`
2. تأكد من أن جميع المتطلبات متوفرة
3. إذا كانت هناك مشاكل، أصلحها قبل المتابعة

### الخطوة 4: تشغيل معالج التثبيت
1. اذهب إلى موقعك: `https://yourdomain.com/install.php`
2. اتبع خطوات المعالج:
   - أدخل بيانات قاعدة البيانات
   - انتظر حتى يتم إنشاء الجداول
   - أكمل عملية التثبيت

### الخطوة 5: تسجيل الدخول
- اذهب إلى: `https://yourdomain.com`
- اسم المستخدم: `admin`
- كلمة المرور: `admin`
- **مهم:** غيّر كلمة المرور فوراً بعد تسجيل الدخول!

### الخطوة 6: فحص صحة النظام
1. اذهب إلى: `https://yourdomain.com/health-check.php`
2. تأكد من أن جميع الفحوصات نجحت
3. إذا كانت هناك مشاكل، راجع دليل استكشاف الأخطاء

### الخطوة 7: تنظيف ملفات التثبيت
1. اذهب إلى: `https://yourdomain.com/cleanup-installation.php`
2. احذف ملفات التثبيت بعد التأكد من عمل النظام
3. أو احذف الملفات يدوياً من File Manager

### الخطوة 8: إعدادات الأمان
1. فعّل شهادة SSL من لوحة تحكم Hostinger
2. تأكد من أن ملف `.htaccess` يعمل بشكل صحيح
3. راجع إعدادات الأمان في النظام

## الاستخدام

### إضافة موظف جديد
1. اذهب إلى "الموظفين" > "إضافة موظف"
2. املأ البيانات المطلوبة
3. ارفع صورة الموظف (اختياري)
4. احفظ البيانات

### تسجيل الحضور
1. اذهب إلى "الحضور والانصراف" > "تسجيل حضور"
2. اختر الموظف والتاريخ
3. سجل وقت الحضور والانصراف
4. أضف أي ملاحظات

### حساب الرواتب
1. اذهب إلى "الرواتب" > "حساب الرواتب"
2. اختر الشهر والسنة
3. راجع البيانات المحسوبة
4. احفظ أو اطبع كشف المرتبات

## الدعم الفني

إذا واجهت أي مشاكل في التثبيت أو الاستخدام:

1. تأكد من أن جميع متطلبات النظام متوفرة
2. راجع ملف `error_log` في مجلد الموقع
3. تأكد من صحة بيانات قاعدة البيانات
4. تأكد من أن مجلد `assets/uploads` قابل للكتابة

## الأمان

- غيّر كلمة مرور المدير الافتراضية فوراً
- احتفظ بنسخ احتياطية منتظمة من قاعدة البيانات
- حدّث النظام بانتظام
- استخدم كلمات مرور قوية
- فعّل شهادة SSL

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**ملاحظة:** هذا النظام مصمم خصيصاً للعمل على استضافة Hostinger Business مع دعم كامل للغة العربية.
