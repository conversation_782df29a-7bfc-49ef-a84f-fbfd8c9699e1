<?php
/**
 * System Requirements Checker
 * 
 * This script checks if the server meets the requirements for the payroll system
 */

// Function to check requirement
function check_requirement($name, $required, $current, $type = 'version') {
    $status = false;
    $class = 'text-red-600';
    $icon = 'fas fa-times-circle';
    
    if ($type === 'version') {
        $status = version_compare($current, $required, '>=');
    } elseif ($type === 'extension') {
        $status = $current;
    } elseif ($type === 'writable') {
        $status = $current;
    }
    
    if ($status) {
        $class = 'text-green-600';
        $icon = 'fas fa-check-circle';
    }
    
    return [
        'name' => $name,
        'required' => $required,
        'current' => $current,
        'status' => $status,
        'class' => $class,
        'icon' => $icon
    ];
}

// Check PHP version
$php_version = phpversion();
$requirements[] = check_requirement('PHP Version', '7.4.0', $php_version, 'version');

// Check MySQL extension
$mysql_available = extension_loaded('mysqli');
$requirements[] = check_requirement('MySQL Extension', 'Required', $mysql_available ? 'Available' : 'Not Available', 'extension');

// Check required PHP extensions
$extensions = [
    'mbstring' => 'Multibyte String',
    'json' => 'JSON',
    'session' => 'Session',
    'fileinfo' => 'File Info',
    'gd' => 'GD (Image Processing)'
];

foreach ($extensions as $ext => $name) {
    $available = extension_loaded($ext);
    $requirements[] = check_requirement($name, 'Required', $available ? 'Available' : 'Not Available', 'extension');
}

// Check directory permissions
$directories = [
    'assets/uploads' => 'Upload Directory',
    'backup' => 'Backup Directory',
    'includes' => 'Includes Directory'
];

foreach ($directories as $dir => $name) {
    if (!file_exists($dir)) {
        @mkdir($dir, 0755, true);
    }
    $writable = is_writable($dir);
    $requirements[] = check_requirement($name, 'Writable', $writable ? 'Writable' : 'Not Writable', 'writable');
}

// Check PHP settings
$memory_limit = ini_get('memory_limit');
$upload_max = ini_get('upload_max_filesize');
$post_max = ini_get('post_max_size');

$all_passed = true;
foreach ($requirements as $req) {
    if (!$req['status']) {
        $all_passed = false;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص متطلبات النظام</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                <i class="fas fa-server text-blue-600 ml-2"></i>
                فحص متطلبات نظام إدارة الموظفين والرواتب
            </h1>
            
            <?php if ($all_passed): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-xl ml-2"></i>
                        <span class="font-bold">ممتاز! جميع المتطلبات متوفرة. يمكنك المتابعة للتثبيت.</span>
                    </div>
                </div>
            <?php else: ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-xl ml-2"></i>
                        <span class="font-bold">تحذير! بعض المتطلبات غير متوفرة. يرجى إصلاحها قبل التثبيت.</span>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-lg mb-2 text-gray-700">معلومات الخادم</h3>
                    <ul class="space-y-1 text-sm">
                        <li><strong>نظام التشغيل:</strong> <?php echo php_uname('s') . ' ' . php_uname('r'); ?></li>
                        <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                        <li><strong>إصدار PHP:</strong> <?php echo $php_version; ?></li>
                        <li><strong>حد الذاكرة:</strong> <?php echo $memory_limit; ?></li>
                        <li><strong>حد رفع الملفات:</strong> <?php echo $upload_max; ?></li>
                        <li><strong>حد POST:</strong> <?php echo $post_max; ?></li>
                    </ul>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-bold text-lg mb-2 text-gray-700">معلومات قاعدة البيانات</h3>
                    <ul class="space-y-1 text-sm">
                        <?php if (extension_loaded('mysqli')): ?>
                            <li><strong>MySQL Client:</strong> <?php echo mysqli_get_client_info(); ?></li>
                        <?php endif; ?>
                        <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                        <li><strong>التشفير:</strong> <?php echo mb_internal_encoding(); ?></li>
                    </ul>
                </div>
            </div>
            
            <h2 class="text-2xl font-bold mb-4 text-gray-800">نتائج الفحص</h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المتطلب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مطلوب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالي</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($requirements as $req): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?php echo $req['name']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $req['required']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $req['current']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="<?php echo $req['class']; ?>">
                                        <i class="<?php echo $req['icon']; ?> ml-1"></i>
                                        <?php echo $req['status'] ? 'متوفر' : 'غير متوفر'; ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="mt-8 text-center">
                <?php if ($all_passed): ?>
                    <a href="install.php" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-play ml-2"></i>
                        بدء التثبيت
                    </a>
                <?php else: ?>
                    <button onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة الفحص
                    </button>
                <?php endif; ?>
                
                <a href="README.md" target="_blank" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 mr-4">
                    <i class="fas fa-book ml-2"></i>
                    دليل التثبيت
                </a>
            </div>
            
            <?php if (!$all_passed): ?>
                <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 class="font-bold text-yellow-800 mb-2">نصائح لحل المشاكل:</h3>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• تأكد من أن إصدار PHP 7.4 أو أحدث</li>
                        <li>• فعّل امتدادات PHP المطلوبة من لوحة تحكم الاستضافة</li>
                        <li>• تأكد من صلاحيات الكتابة للمجلدات المطلوبة</li>
                        <li>• اتصل بدعم الاستضافة إذا كنت بحاجة لمساعدة</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
